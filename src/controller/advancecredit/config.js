const util = require('../../util/util')

const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'

const config = {
  decryptionKey: util[env].secondaryEncrptionKey,
  api_timeout: 60000,
  panVerificationUrl: !util.isProduction() ? 'https://baas.airpay.ninja/node/api/panVerificationv1' : '',
  documentLinkUrl: !util.isProduction() ? 'https://retaila.airpay.ninja/api/agreement-generate' : '',
  docLinkEncryptKey: 'OlorK9nZbeqBoG1oTJilbPl6S4sb5cyT',
  permittedChar: '0123456789abcdefghijklmnopqrstuvwxyz',
  dockLinkSource: 'd2Vi',
  otp_type: 'ADVCR',
  otp_template_id: util.templateid.ADVCR,
  IFSC_CHECKSUM_SECRETKEY: 'b0B1c#dsaf45ADSF5645adf0215',
  APIKEY: 'xvIvkuxKczCf8sapHZv5ia9qzPEB9iPRvtLr4iJn4SzmMjfuWgVvSsXvyw==1',
  AFFILIATE: 'AF0022AF1632473204',
  AIRPAYKEY: 'AL0006AL1625833854',
  CIBILURL: 'https://baas.airpay.ninja/node/api/cibil',
  CIBILAFFILIATE: '0391614949636',
  CIBILAPIKEY: 'xYhdbhbcxKczCf8sapMnhda9qzPEB9iPRvtLrd89sdkjf390jhsUtgh',
  CIBILENROLLMENTAPITYPE: 'enrollment',
  CIBILCUSTOMERAPITYPE: 'GetCustomerAssets',
  CIBILPARTNERCUSTOMERID: '957767828',
  esign_generate_req_url: !util.isProduction() ? 'https://esign.airpay.co.in/public/api/generateRequest' : '',
  esign_generate_req_xml_url: !util.isProduction() ? 'https://esign.airpay.co.in/api/generateRequestXml' : '',
  esign_generate_res_xml_url: !util.isProduction() ? 'https://esign.airpay.co.in/api/generateResponseXml' : '',
  ESIGN_AFFILIATE: 'AF0014AF1628255127',
  ESIGN_CHECKSUM: 'sefefer',
  ESIGN_AIRPAYKEY: 'xvIvkuxKczCf8sapHZv5ia9qzPEB9iPRvtLr4iJn4SzmMjfuWgVvSsXvyw==',
  esign_docrefid: 'QCM33KH',
  TRANSACTION_TYPE_ARR: [2, 10, 50, 24, 75, 5, 6, 17],
  ESIGN_BASE_URL: 'https://retaila.airpay.ninja/credit-advance/agreement-generate/'
}

const displayFormSection = {
  genInfo: 'GEN_INFO',
  otp: 'OTP',
  credQuote: 'CRED_QUOTE',
  eSign: 'E_SIGN',
  credStatus: 'CRED_STATUS'
}

module.exports = { config, displayFormSection }
