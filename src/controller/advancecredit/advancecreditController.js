const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const util = require('../../util/util')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const validator = require('../../util/validator')
const otpController = require('../otp/otpController')
const { config, displayFormSection } = require('./config')
const crypto = require('crypto')
const Axios = require('axios')
const moment = require('moment-timezone')
const qs = require('qs')
const common = require('../../util/common')

class advancecreditController extends DAO {
  constructor() {
    super()
    this._SQSENABLE = false
    this._SQSGROUPID = null
    this._SQSDATA = []
  }

  static get TABLE_NAME() {
    return 'ma_advance_credit_requests'
  }

  static get PRIMARY_KEY() {
    return 'ma_user_id'
  }

  static get SQSENABLE() {
    return this._SQSENABLE
  }

  static set SQSENABLE(flag) {
    this._SQSENABLE = flag
  }

  static get SQSGROUPID() {
    return this._SQSGROUPID
  }

  static set SQSGROUPID(groupid) {
    this._SQSGROUPID = groupid
  }

  static async requestGeneration(_,fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'requestGeneration', type: 'request', fields: { fields } })
    const connection = await mySQLWrapper.getConnectionFromPool()
    // Request body validation
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validateTransferParamRes.status != 200) return validateTransferParamRes
 
    try {
      const fetchCreditFormQuery = `SELECT ma_advance_credit_requests_id ,approved_status FROM ma_advance_credit_requests WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} 
      AND addedon BETWEEN DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 3 MONTH) AND CURRENT_TIMESTAMP
      order by ma_advance_credit_requests_id  desc LIMIT 1`

      log.logger({ pagename: require('path').basename(__filename), action: 'requestGeneration', type: 'fetch credit advance form data - query', fields: fetchCreditFormQuery })
  
      const creditFormResult = await this.rawQuery(fetchCreditFormQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'requestGeneration', type: 'fetch credit advance form data - result', fields: creditFormResult })

      if (creditFormResult.length > 0) {
        return { status: 400, respcode: 12694, message: errorMsg.responseCode[12694], action_code: 1001 }
      }
 
      // Check merchant
      const env = process.env.NODE_ENV ? process.env.NODE_ENV : 'development'
      const decryptionKey = util[env].secondaryEncrptionKey
      const fetchMerchantQry = `SELECT firstname, middlename,lastname,distributer_user_master_id, user_type,CAST(AES_DECRYPT(pan,'${decryptionKey}') AS CHAR) as pan FROM ma_user_master WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid} LIMIT 1`

      console.log("fetchMerchantQry-SQL", fetchMerchantQry)
      const fetchMerchantDataResult = await this.rawQuery(fetchMerchantQry, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'requestGeneration', type: 'merchant Details- Response', fields: { fetchMerchantDataResult, fetchMerchantQry } })
 
 
      if (fetchMerchantDataResult.length <= 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1003] }
      }
      const fetchRiskQry = `SELECT risk_category FROM ma_user_master_risk_details WHERE ma_user_id = ${fields.ma_user_id} AND user_id = ${fields.userid} LIMIT 1`
      const fetchRiskResult = await this.rawQuery(fetchRiskQry, connection)
      let riskCat = ""
      if (fetchRiskResult.length > 0) {
        riskCat = fetchRiskResult[0]?.risk_category
      }
 
      log.logger({ pagename: require('path').basename(__filename), action: 'requestGeneration', type: 'merchant type', fields: fetchMerchantDataResult[0]?.user_type })
 
      const merchantType = fetchMerchantDataResult[0]?.user_type
      const panNumber = fetchMerchantDataResult[0]?.pan ?? null
      const merchantName = fetchMerchantDataResult[0]?.firstname + ' ' + fetchMerchantDataResult[0]?.lastname
 
      const transactionTypeArr = config.TRANSACTION_TYPE_ARR
 
      let sumAmount = 0
      if (merchantType == "RT") {
        const volumeQry = `SELECT SUM(amount) AS average_amount FROM ma_transaction_master WHERE ma_user_id =  ${fields.ma_user_id} AND transaction_type in (${transactionTypeArr})
                         AND addedon  >= DATE_SUB(CURRENT_DATE, INTERVAL 3 MONTH); `
        const volumeRes = await this.rawQuery(volumeQry, connection);

 
        sumAmount = volumeRes[0]?.average_amount
      }
 
      else if (merchantType == "DT") {
 
        const fetchProfileIdQry = `SELECT DISTINCT profileid FROM ma_user_master
                                 WHERE distributer_user_master_id = ${fields.ma_user_id}`;
        const fetchProfileIdRes = await this.rawQuery(fetchProfileIdQry, connection);
 
        const profileIds = fetchProfileIdRes.map(row => row.profileid);
        log.logger({ pagename: require('path').basename(__filename), action: 'RT ProfileIds', type: 'profileIds - RT', fields:profileIds })
 
 
        if (profileIds.length > 0) {
          const { totalAmount } = await this.calculateSumForProfiles.call(this, profileIds, transactionTypeArr, connection);
          log.logger({ pagename: require('path').basename(__filename), action: 'RT ProfileIds', type: 'response amount for - DT', fields:totalAmount })
 
          sumAmount = totalAmount;
        }
      }
      else if (merchantType == "SDT") {
        const fetchDTQry = `SELECT DISTINCT profileid FROM ma_user_master
                          WHERE distributer_user_master_id = ${fields.ma_user_id}`;
        const fetchDTRes = await this.rawQuery(fetchDTQry, connection);
 
        const dtIds = fetchDTRes.map(row => row.profileid);
        log.logger({ pagename: require('path').basename(__filename), action: 'requestGeneration', type: 'Child of DT: ', fields: dtIds })
 
 
        if (dtIds.length > 0) {
          const chunkSize = 10;
          let totalAmountSDT = 0;
 
          for (let i = 0; i < dtIds.length; i += chunkSize) {
            const dtChunk = dtIds.slice(i, i + chunkSize);
            log.logger({ pagename: require('path').basename(__filename), action: 'requestGeneration', type: 'chunks of DT: ', fields: dtChunk })
 
 
            for (const dtUserId of dtChunk) {
              log.logger({ pagename: require('path').basename(__filename), action: 'requestGeneration', type: 'Processing DT user_id: ', fields: dtUserId })
 
 
              const fetchProfileIdQry = `SELECT DISTINCT profileid FROM ma_user_master
                                       WHERE distributer_user_master_id = ${dtUserId}`;
              const fetchProfileIdRes = await this.rawQuery(fetchProfileIdQry, connection);
              log.logger({ pagename: require('path').basename(__filename), action: 'requestGeneration', type: 'fetchProfileId- Resp: ', fields: fetchProfileIdRes })
 
              const profileIds = fetchProfileIdRes.map(row => row.profileid);
 
 
              if (profileIds.length > 0) {
                const { totalAmount } = await this.calculateSumForProfiles.call(this, profileIds, transactionTypeArr, connection);
 
                log.logger({ pagename: require('path').basename(__filename), action: 'requestGeneration', type: 'totalAmountSDT:Amt ', fields: totalAmountSDT })
 
                totalAmountSDT += totalAmount;
              }
            }
          }
 
          sumAmount = totalAmountSDT;
          console.log(`Final sumAmount for SDT: ${sumAmount}`);
 
        }
      }
      const datetime = moment.tz('Asia/Kolkata').format('YYYY-MM-DD HH:MM:SS')
      console.log("datetimeis", datetime)
      const requestGenQury = `INSERT INTO ma_advance_credit_requests
         (ma_user_id, userid, merchant_name, pan, request_date, merchant_type, avg_business_volume, risk_category, approved_status)
       VALUES
         (${fields.ma_user_id},${fields.userid}, '${merchantName}', ${panNumber ? `CAST(AES_ENCRYPT('${panNumber}', '${decryptionKey}') AS BINARY)` : 'NULL'},'${datetime}','${merchantType}',${sumAmount},'${riskCat}' ,'P')`
      log.logger({ pagename: require('path').basename(__filename), action: 'requestGeneration', type: 'insertquery: ', fields: requestGenQury })

      const response = await this.rawQuery(requestGenQury, connection)
      const requestId = response?.insertId || null;

      console.log("requestIdis", requestId)
 
      log.logger({ pagename: require('path').basename(__filename), action: 'requestGeneration', type: 'insertquery- Res: ', fields: response })
 
      return { status: 200, message: errorMsg.responseCode[12692], respcode: 1000 , action_code: 1000}
    }
    catch (error) {
      log.logger({ pagename: 'advanceCreditController.js', action: 'requestGeneration', type: 'response-error', fields: error })
      return { status: 400, respcode: 1028, message: errorMsg.responseCode[1028], action_code: 1001 }
    } finally {
      connection.release()
    }
 
  }

   static async calculateSumForProfiles(profileIds, transactionTypeArr, connection, chunkSize = 10) {
    console.log("calculateSumForProfiles-line")
    let totalAmount = 0;
    const perChunkSums = [];
 
    for (let i = 0; i < profileIds.length; i += chunkSize) {
      const profileChunk = profileIds.slice(i, i + chunkSize);
 
      const sumQry = `SELECT SUM(amount) AS chunk_amount
                    FROM ma_transaction_master
                    WHERE ma_user_id IN (${profileChunk.join(',')})
                      AND addedon >= DATE_SUB(CURRENT_DATE, INTERVAL 3 MONTH)
                      AND transaction_type in (${transactionTypeArr})`;
      console.log("Running SUM for profiles:", profileChunk);
      console.log("SQL:", sumQry);
 
      const sumRes = await this.rawQuery(sumQry, connection);
      const chunkAmount = sumRes[0]?.chunk_amount || 0;
 
      console.log(`Chunk sum amount: ${chunkAmount}`);
 
      totalAmount += chunkAmount;
 
      perChunkSums.push({
        profileChunk: profileChunk,
        chunkAmount: chunkAmount
      });
    }
 
    console.log(`Final totalAmount: ${totalAmount}`);
    return {
      totalAmount,
      perChunkSums
    };
  }

  static async fetchRequestDetails (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'fetchRequestDetails', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      // check existing form record
      const fetchRequestQuery = `SELECT * FROM ma_advance_credit_requests WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_requests_id = ${fields.request_id} AND approved_status = 'A' ORDER BY addedon DESC LIMIT 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchRequestDetails', type: 'fetch advance credit form - query', fields: fetchRequestQuery })

      const fetchRequestResult = await this.rawQuery(fetchRequestQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchRequestDetails', type: 'fetch advance credit - result', fields: fetchRequestResult })

      return fetchRequestResult || []
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchRequestDetails', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async fetchFormDetails (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'fetchFormDetails', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      // check existing form record
      const fetchFormQuery = `SELECT ma_advance_credit_form_data_id, ma_advance_credit_requests_id as request_id, ma_user_id, userid, firstname, lastname, email_id, mobile_number, merchant_type, address, personal_address, pincode, CAST(AES_DECRYPT(pan,'${config.decryptionKey}') AS CHAR) as pan, pan_name, dob, fathername, gender, pan_verify_attempt_count, pan_verify_status, otp_verify_status, form_approval_status, requested_amount, navigation FROM ma_advance_credit_form_data WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND addedon >= DATE_SUB(NOW(), INTERVAL 3 MONTH) ORDER BY addedon DESC LIMIT 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchFormDetails', type: 'fetch advance credit form - query', fields: fetchFormQuery })

      const fetchFormResult = await this.rawQuery(fetchFormQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchFormDetails', type: 'fetch advance credit - result', fields: fetchFormResult })

      return fetchFormResult || []
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchFormDetails', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async getAdvanceCreditForm (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAdvanceCreditForm', type: 'request', fields: fields })
    // const connection = await mySQLWrapper.getConnectionFromPool()

    // Request body validation
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid', 'request_id'])
    if (validateTransferParamRes.status != 200) return validateTransferParamRes

    try {
      // check initial request
      const requestDetails = await this.fetchRequestDetails(fields)
      if (requestDetails.length <= 0) return { status: 400, message: 'Fail: Advance credit request not found or not approved yet.', respcode: 1001, action_code: 1001 }

      // retrieve data from existing form record
      const formDetails = await this.fetchFormDetails(fields)
      if (formDetails.length > 0) {
        console.log('Is form record exists >>>>>>>>>>', 'true', formDetails[0].ma_advance_credit_form_data_id)

        switch (formDetails[0]?.navigation) {
          case '': // ''
          case displayFormSection.genInfo: // GEN_INFO
          case displayFormSection.otp: // OTP
            return await this.getMerchantDetails(_, fields)
          case displayFormSection.credQuote: // CRED_QUOTE
            return await this.getCreditLimit(_, fields)
          case displayFormSection.eSign: // E_SIGN
            return await this.getDocLink(_, fields)
          case displayFormSection.credStatus: // CRED_STATUS
            return await this.getFormApprovalStatus(_, fields)
          default:
            return await this.getMerchantDetails(_, fields)
        }
      }
      return await this.getMerchantDetails(_, fields) // new request
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getAdvanceCreditForm', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } 
    // finally {
    //   connection.release()
    // }
  }

  static async getMerchantDetails (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantDetails', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()

    // Request body validation
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validateTransferParamRes.status != 200) return validateTransferParamRes

    try {
      // retrieve data from existing form record
      const formDetails = await this.fetchFormDetails(fields)
      if (formDetails.length > 0) {
        console.log('Existing form record exists >>>>>>>>>>>>>>>', 'true') 
        // navigate user to gen info section to generate otp again
        if (formDetails[0].otp_verify_status == 'N') formDetails[0].navigation = displayFormSection.genInfo

        // remove data
        delete formDetails[0].pan_verify_status
        delete formDetails[0].otp_verify_status
        delete formDetails[0].form_approval_status
        delete formDetails[0].requested_amount

        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, data: formDetails[0] }
      }

      // retrieve data from user master
      const fetchMerchantDetailsQuery = `SELECT profileid as ma_user_id, userid, firstname, lastname, email_id, mobile_id as mobile_number, user_type as merchant_type, address, personal_address, pincode, gender, CAST(AES_DECRYPT(pan,'${config.decryptionKey}') AS CHAR) as pan, dob FROM ma_user_master WHERE profileid = ${fields.ma_user_id} AND userid = ${fields.userid} LIMIT 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantDetails', type: 'fetch merchant details - query', fields: fetchMerchantDetailsQuery })

      const merchantDetailsResult = await this.rawQuery(fetchMerchantDetailsQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantDetails', type: 'fetch merchant details - result', fields: merchantDetailsResult })

      if (merchantDetailsResult.length > 0) {
        const merchantData = merchantDetailsResult[0]
        merchantData.navigation = displayFormSection.genInfo // GEN_INFO

        // adding additional parameters as null for frontend
        merchantData.ma_advance_credit_form_data_id = null
        merchantData.request_id = null
        // merchantData.pan = ''
        merchantData.pan_name = ''
        // merchantData.dob = ''
        merchantData.fathername = ''
        // merchantData.pan_verify_status = ''
        // merchantData.otp_verify_status = ''
        // merchantData.form_approval_status = ''
        // merchantData.requested_amount = null

        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, data: merchantData }
      } else {
        return { status: 200, message: errorMsg.responseCode[1002], respcode: 1000, action_code: 1000, data: [] }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantDetails', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  /** 
  static async submitMerchantDetails (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()

    // Request body validation
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid', 'firstname', 'lastname', 'email_id', 'mobile_number', 'merchant_type', 'address', 'pincode', 'pan', 'pan_name', 'dob', 'request_id', 'gender'])
    if (validateTransferParamRes.status != 200) return validateTransferParamRes

    try {
      // check whether form record exists
      const formDetails = await this.fetchFormDetails(fields)

      if (formDetails.length > 0) {
        // send otp
        const sendOtpResp = await this.sendOtp(_, fields)
        console.log("sendOtpResp >>>", sendOtpResp)
        
        if (!sendOtpResp.status) return sendOtpResp
          
        return { status: 200, respcode: 2002, message: errorMsg.responseCode[2002] + '. Please check the registered mobile number - ' + ('' + fields.mobile_number).replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '*'), aggregator_order_id: sendOtpResp.data.aggregator_order_id, mobile_number: fields.mobile_number, data: { navigation: displayFormSection.otp, request_id: formDetails[0].request_id || fields.request_id } }
      }

      // pan verification
      const panDetails = {
        pan: fields.pan || '',
        name: fields.pan_name || '',
        dob: fields.dob || '',
        fathername: fields.fathername || ''
      }

      const info = { ma_user_id: fields.ma_user_id, userid: fields.userid, request_id: fields.request_id }

      const resp = await this.verifyPanDetails(panDetails, connection, info)
      log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'axios api response', fields: resp })

      const isPanValid = !!(resp?.apiResponse?.status == 200 && resp?.apiResponse?.data?.name == 'Y' && resp?.apiResponse?.data?.DOB == 'Y')
      console.log('Is PAN valid >>>>>>>>>', isPanValid)

      // api call successful
      if (resp?.status == 200) {
        // pan validation successful
        if (isPanValid) {

          const temp = {
            isPanVerified: 'Y', // yes
            isOtpVerified: 'N', // no
            formApprovalStatus: 'P', // pending
            navigateFlag: displayFormSection.otp // proceed to otp section
          }
          
          const insertDetails = `INSERT INTO ma_advance_credit_form_data (ma_advance_credit_requests_id, ma_user_id, userid, firstname, lastname, email_id, mobile_number, merchant_type, address, pincode, pan, pan_name, dob, fathername, gender, pan_verify_status, otp_verify_status, form_approval_status, navigation) VALUES (${fields.request_id}, ${fields.ma_user_id}, ${fields.userid}, '${fields.firstname}', '${fields.lastname}', '${fields.email_id}', '${fields.mobile_number}', '${fields.merchant_type}', '${fields.address}', '${fields.pincode}', AES_ENCRYPT('${fields.pan}', '${config.decryptionKey}'), '${fields.pan_name}', '${fields.dob}', '${fields.fathername}', '${fields.gender}', '${temp.isPanVerified}', '${temp.isOtpVerified}', '${temp.formApprovalStatus}', '${temp.navigateFlag}')`
          log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'insert merchant form details - query', fields: insertDetails })

          const insertDetailsResult = await this.rawQuery(insertDetails, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'insert merchant form details - result', fields: insertDetailsResult })

          // send otp
          const sendOtpResp = await this.sendOtp(_, fields)
          console.log("sendOtpResp >>>", sendOtpResp)

          if (!sendOtpResp.status) return sendOtpResp
          
          return { status: 200, respcode: 2002, message: errorMsg.responseCode[2002] + '. Please check the registered mobile number - ' + ('' + fields.mobile_number).replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '*'), aggregator_order_id: sendOtpResp.data.aggregator_order_id, mobile_number: fields.mobile_number, data: { navigation: temp.navigateFlag, request_id: fields.request_id } }
        } else {
          return { status: 400, message: 'PAN verification failed', respcode: 1001, action_code: 1001 }
        }
      }
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  } 
  */

  static async submitMerchantDetails (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()

    // Request body validation
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid', 'firstname', 'lastname', 'email_id', 'mobile_number', 'merchant_type', 'personal_address', 'pincode', 'pan', 'pan_name', 'dob', 'request_id', 'gender'])
    if (validateTransferParamRes.status != 200) return validateTransferParamRes

    try {
      // check whether form record exists
      const formDetails = await this.fetchFormDetails(fields)

      if (formDetails.length > 0) {
        // pan verified - send otp
        if (formDetails[0].pan_verify_status === 'Y') {
          // send otp
          const sendOtpResp = await this.sendOtp(_, fields)
          console.log("sendOtpResp >>>", sendOtpResp)
          
          if (!sendOtpResp.status) return sendOtpResp
            
          return { status: 200, respcode: 1000, action_code: 1000, message: errorMsg.responseCode[2002] + '. Please check the registered mobile number - ' + ('' + fields.mobile_number).replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '*'), data: { aggregator_order_id: sendOtpResp.data.aggregator_order_id, mobile_number: fields.mobile_number, navigation: displayFormSection.otp, request_id: formDetails[0].request_id || fields.request_id } }
        }

        // pan not verified - update pan details first
        const updatePanDataQuery = `UPDATE ma_advance_credit_form_data SET pan = AES_ENCRYPT('${fields.pan}', '${config.decryptionKey}'), pan_name = '${fields.pan_name}', dob = '${fields.dob}', fathername = '${fields.fathername}' WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_form_data_id = ${formDetails[0].ma_advance_credit_form_data_id}`
        log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'update pan data - query', fields: updatePanDataQuery })

        const updatePanDataResult = await this.rawQuery(updatePanDataQuery, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'update pan data - result', fields: updatePanDataResult })
		
        // verify pan
        const resp = await this.verifyPanDetails(fields, connection)
        const isPanValid = !!(resp?.apiResponse?.status == 200 && resp?.apiResponse?.data?.name == 'Y' && resp?.apiResponse?.data?.DOB == 'Y')
		   
        if (isPanValid) {
          // update details
          const updateCountQuery = `UPDATE ma_advance_credit_form_data SET pan_verify_status = 'Y', navigation = '${displayFormSection.otp}' WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_form_data_id = ${formDetails[0].ma_advance_credit_form_data_id}`
          log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'update pan verification status - query', fields: updateCountQuery })

          const updateCountResult = await this.rawQuery(updateCountQuery, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'update pan verification status - result', fields: updateCountResult })
          
          // send otp
          const sendOtpResp = await this.sendOtp(_, fields)
          console.log("sendOtpResp >>>", sendOtpResp)
          
          if (!sendOtpResp.status) return sendOtpResp
          
          return { status: 200, respcode: 1000, action_code: 1000, message: errorMsg.responseCode[2002] + '. Please check the registered mobile number - ' + ('' + fields.mobile_number).replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '*'), data: { aggregator_order_id: sendOtpResp.data.aggregator_order_id, mobile_number: fields.mobile_number, navigation: displayFormSection.otp, request_id: formDetails[0].request_id || fields.request_id } }
        }
		
		    // pan invalid - Update the error count
	    	let navStatus = displayFormSection.genInfo
        
        // update pan verification error count until 3 attempts
        if (formDetails[0].pan_verify_attempt_count <= 2) {
          const updateCountQuery = `UPDATE ma_advance_credit_form_data SET pan_verify_attempt_count = pan_verify_attempt_count + 1 WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_form_data_id = ${formDetails[0].ma_advance_credit_form_data_id}`
          log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'update pan error count - query', fields: updateCountQuery })

          const updateCountResult = await this.rawQuery(updateCountQuery, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'update pan error count - result', fields: updateCountResult })
        }
		
        // Reject form on 3rd attempt
        if (formDetails[0].pan_verify_attempt_count >= 2) {
          const updatePanStatusQuery = `UPDATE ma_advance_credit_form_data SET form_approval_status = 'R', navigation = '${displayFormSection.credStatus}' WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_form_data_id = ${formDetails[0].ma_advance_credit_form_data_id}`
          log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'update pan status - query', fields: updatePanStatusQuery })

          const updatePanStatusResult = await this.rawQuery(updatePanStatusQuery, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'update pan status - result', fields: updatePanStatusResult })
          
          navStatus = displayFormSection.credStatus
        }
		    return { status: 400, message: 'PAN verification failed', respcode: 1001, action_code: 1001, data: { navigation: navStatus, request_id: formDetails[0].request_id || fields.request_id } }
      }
	  
	    // form does not exists, insert new record -------------
	  
      const temp = {
        isPanVerified: 'N', // no
        isOtpVerified: 'N', // no
        formApprovalStatus: 'P', // form pending to fill
        navigateFlag: displayFormSection.genInfo
      }
	  
      const insertDetails = `INSERT INTO ma_advance_credit_form_data (ma_advance_credit_requests_id, ma_user_id, userid, firstname, lastname, email_id, mobile_number, merchant_type, address, personal_address, pincode, pan, pan_name, dob, fathername, gender, pan_verify_status, otp_verify_status, form_approval_status, navigation) VALUES (${fields.request_id}, ${fields.ma_user_id}, ${fields.userid}, '${fields.firstname}', '${fields.lastname}', '${fields.email_id}', '${fields.mobile_number}', '${fields.merchant_type}', '${fields.address || ''}', '${fields.personal_address}', '${fields.pincode}', AES_ENCRYPT('${fields.pan}', '${config.decryptionKey}'), '${fields.pan_name}', '${fields.dob}', '${fields.fathername || ''}', '${fields.gender}', '${temp.isPanVerified}', '${temp.isOtpVerified}', '${temp.formApprovalStatus}', '${temp.navigateFlag}')`
      log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'insert merchant form details - query', fields: insertDetails })

      const insertDetailsResult = await this.rawQuery(insertDetails, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'insert merchant form details - result', fields: insertDetailsResult })
	  
	    if (!insertDetailsResult?.affectedRows) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
	    const recordRowId = insertDetailsResult.insertId

      const resp = await this.verifyPanDetails(fields, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'axios api response', fields: resp })

      // api call successful
      if (resp?.status == 200) {
        // is pan verified
		    const isPanValid = !!(resp?.apiResponse?.status == 200 && resp?.apiResponse?.data?.name == 'Y' && resp?.apiResponse?.data?.DOB == 'Y')
        console.log('Is PAN valid >>>>>>>>>', isPanValid)
	  
        // pan validation successful
        if (isPanValid) {
          // send to next section
          temp.isPanVerified = 'Y'
          temp.navigateFlag = displayFormSection.otp

          // update pan status
          const updatePanStatusQuery = `UPDATE ma_advance_credit_form_data SET pan_verify_status = '${temp.isPanVerified}', navigation = '${temp.navigateFlag}' WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_form_data_id = ${recordRowId}`
          log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'update pan status - query', fields: updatePanStatusQuery })

          const updatePanStatusResult = await this.rawQuery(updatePanStatusQuery, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'update pan status - result', fields: updatePanStatusResult })

          // send otp
          const sendOtpResp = await this.sendOtp(_, fields)
          console.log("sendOtpResp >>>", sendOtpResp)
          if (!sendOtpResp.status) return sendOtpResp
          
          return { status: 200, respcode: 1000, action_code: 1000, message: errorMsg.responseCode[2002] + '. Please check the registered mobile number - ' + ('' + fields.mobile_number).replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '*'), data: { aggregator_order_id: sendOtpResp.data.aggregator_order_id, mobile_number: fields.mobile_number, navigation: temp.navigateFlag, request_id: fields.request_id } }
        } else {
          // pan not verified - update pan verify attempt count
          const updateCountQuery = `UPDATE ma_advance_credit_form_data SET pan_verify_attempt_count = pan_verify_attempt_count + 1 WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_form_data_id = ${recordRowId}`
          log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'update pan error  verification attempt - query', fields: updateCountQuery })

          const updateCountResult = await this.rawQuery(updateCountQuery, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'update pan error verification attempt - result', fields: updateCountResult })
		
          return { status: 400, message: 'PAN verification failed', respcode: 1001, action_code: 1001, data: { navigation: temp.navigateFlag, request_id: fields.request_id } }
        }
      }
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'submitMerchantDetails', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async verifyPanDetails (fields, connection) {
    try {
      const data = {
        pan: fields.pan || '',
        name: fields.pan_name || '',
        dob: fields.dob || '',
        fathername: fields.fathername || ''
      }

      const headers = {
        APIKEY: config.APIKEY,
        CHECKSUM: await this.generateChecksum(data, config.IFSC_CHECKSUM_SECRETKEY),
        AFFILIATE: config.AFFILIATE,
        AIRPAYKEY: config.AIRPAYKEY
      }

      const response = await this.doApiCall({
        ma_user_id: fields.ma_user_id, 
        userid: fields.userid, 
        request_id: fields.request_id,
        api_name: 'pan_verification_api',
        url: config.panVerificationUrl,
        method: 'post',
        data: data,
        config: { headers }
      }, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyPanDetails', type: 'response data', fields: response })

      return response
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyPanDetails', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  static async generateChecksum (dataArray, IFSC_CHECKSUM_SECRETKEY) {
    let chkStrVal = ''
    if (dataArray && typeof dataArray === 'object') {
    // Concatenate all values in order
      for (const key in dataArray) {
        if (Object.prototype.hasOwnProperty.call(dataArray, key)) {
          chkStrVal += dataArray[key]
        }
      }
    }

    const checksumStr = crypto
      .createHash('sha256')
      .update(chkStrVal + IFSC_CHECKSUM_SECRETKEY)
      .digest('hex')
    return checksumStr
  }

  static async doApiCall (requestInfo, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doApiCall', type: 'request', fields: requestInfo })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    let response

    try {
      switch (requestInfo.method.toLowerCase()) {
        case 'post':
          response = await Axios.post(requestInfo.url, requestInfo.data, requestInfo.config)
          break
        case 'get':
          response = await Axios.get(requestInfo.url, requestInfo.data, requestInfo.config)
          break
        case 'put':
          response = await Axios.put(requestInfo.url, requestInfo.data, requestInfo.config)
          break
        case 'patch':
          response = await Axios.patch(requestInfo.url, requestInfo.data, requestInfo.config)
          break
        case 'delete':
          response = await Axios.delete(requestInfo.url, requestInfo.data, requestInfo.config)
          break
        default:
          return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'doApiCall', type: 'response', fields: { data: response.data } })
      const requestParams = { api_name: requestInfo.api_name, url: requestInfo.url, data: requestInfo.data, config: requestInfo.config, method: requestInfo.method }

      // Save the request and response data
      await this.insertApiLog({
        api_name: requestInfo.api_name,
        request: JSON.stringify(requestParams).replace(/'/gm, '\'').replace(/\\/gm, '\\\\'),
        response: JSON.stringify(response.data).replace(/'/gm, '\'').replace(/\\/gm, '\\\\'),
        ma_user_id: requestInfo.ma_user_id,
        userid: requestInfo.userid,
        request_id: requestInfo.request_id
      }, connection)

      return {
        status: response.status,
        respcode: response.status === 200 ? 1000 : 1144,
        message: errorMsg.responseCode[response.status === 200 ? 1000 : 1144],
        apiResponse: response.data
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'doApiCall', type: 'error', fields: error })

      const requestParams = {
        api_name: requestInfo.api_name,
        url: requestInfo.url,
        data: requestInfo.data,
        config: requestInfo.config,
        method: requestInfo.method
      }

      // Handle Axios error with response data
      if (error.response && error.response.status && error.response.data) {
        const { status, data } = error.response
        log.logger({ pagename: require('path').basename(__filename), action: 'doApiCall', type: 'error - status, data', fields: { status, data } })

        try {
          // Save request and error response data
          await this.insertApiLog({
            api_name: requestInfo.api_name,
            request: JSON.stringify(requestParams).replace(/'/g, '\'').replace(/\\/g, '\\\\'),
            response: JSON.stringify(data).replace(/'/g, '\'').replace(/\\/g, '\\\\'),
            ma_user_id: requestInfo.ma_user_id,
            userid: requestInfo.userid,
            request_id: requestInfo.request_id
          }, connection)
        } catch (insertError) {
          log.logger({ pagename: require('path').basename(__filename), action: 'doApiCall', type: 'error - insertError', fields: insertError })
        }

        return {
          status: 400,
          respcode: 1001,
          message: errorMsg.responseCode[1001],
          apiResponse: data
        }
      }

      if (error.isAxiosError && error.code === 'ECONNABORTED') {
        console.log('CONNECTION_TIMEOUT: ' + requestInfo.api_name)
        return { status: 301, respcode: 1145, message: errorMsg.responseCode[1145] }
      }
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } 
    finally {
      if (tempConnection) {
        console.log('connection released')
        connection.release()
      }
    }
  }

  static async insertApiLog (fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'insertApiLog', type: 'request', fields: fields })

    try {
      const insertLogQuery = `INSERT INTO ma_advance_credit_api_log (api_name, request, response, ma_user_id, userid, request_id) VALUES ('${fields.api_name}', '${fields.request}', '${fields.response}', ${fields.ma_user_id}, ${fields.userid}, ${fields.request_id})`
      log.logger({ pagename: require('path').basename(__filename), action: 'insertApiLog', type: 'insert api req resp - query', fields: insertLogQuery })

      const insertLogResult = await this.rawQuery(insertLogQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'insertApiLog', type: 'insert api req resp - result', fields: insertLogResult })
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'insertApiLog', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  static async updateNavigation (fields, connection) {
    log.logger({ pagename: require('path').basename(__filename), action: 'updateNavigation', type: 'request', fields: fields })

    // Request body validation
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid', 'request_id', 'navigation'])
    if (validateTransferParamRes.status != 200) return validateTransferParamRes

    try {
      const updateNavQuery = `UPDATE ma_advance_credit_form_data SET navigation = '${fields.navigation}' WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_requests_id = ${fields.request_id}`
      log.logger({ pagename: require('path').basename(__filename), action: 'updateNavigation', type: 'update navigation - query', fields: updateNavQuery })

      const updateNavResult = await this.rawQuery(updateNavQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'updateNavigation', type: 'update navigation - result', fields: updateNavResult })
      
      if (updateNavResult.affectedRows > 0) {
        console.log('Navigation updated to ', fields.navigation)
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
      } else {
        return { status: 400, respcode: 1023, message: errorMsg.responseCode[1023] }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'updateNavigation', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  static async sendOtp (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sendOtp', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()

    // Request body validation
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid', 'mobile_number'])
    if (validateTransferParamRes.status != 200) return validateTransferParamRes

    try {
      const resp = await otpController.sentOtp(fields.ma_user_id, fields.userid, parseInt(fields.mobile_number), config.otp_type, { otpTypeForTemplateId: config.otp_template_id }, '', connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'sendOtp', type: 'send advance credit otp - result', fields: resp })
      if (resp?.status) {
        resp.status = 200
        resp.action_code = 1000
      } else {
        resp.status = 400
        resp.action_code = 1001
      }
      return resp
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sendOtp', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async resendOtp (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'resendOtp', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()

    // Request body validation
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid', 'mobile_number', 'aggregator_order_id'])
    if (validateTransferParamRes.status != 200) return validateTransferParamRes

    try {
      const otpResp = await otpController.resentOtp(parseInt(fields.mobile_number), fields.aggregator_order_id, config.otp_type, { templateId: config.otp_template_id })
      log.logger({ pagename: require('path').basename(__filename), action: 'resendOtp', type: 'send advance credit otp - result', fields: otpResp })
      if (otpResp?.status) {
        otpResp.status = 200
        otpResp.action_code = 1000
      } else {
        otpResp.status = 400
        otpResp.action_code = 1001
      }
      return otpResp
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'resendOtp', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async verifyOtp (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtp', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()

    // Request body validation
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid', 'aggregator_order_id', 'otp', 'request_id'])
    if (validateTransferParamRes.status != 200) return validateTransferParamRes

    try {
      const verifyResp = await otpController.verifyOtp(fields.aggregator_order_id, config.otp_type, fields.otp)
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtp', type: 'verify advance credit otp - result', fields: verifyResp })

      if (verifyResp.status) {
        // update otp verify status
        const updateOtpInfoQuery = `UPDATE ma_advance_credit_form_data SET otp_verify_status = 'Y' WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_requests_id = ${fields.request_id}`
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtp', type: 'update otp verified - query', fields: updateOtpInfoQuery })

        const updateOtpResult = await this.rawQuery(updateOtpInfoQuery, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtp', type: 'update otp verified - result', fields: updateOtpResult })

        // update navigation
        const navigateFlag = displayFormSection.credQuote
        await this.updateNavigation({ ma_user_id: fields.ma_user_id, userid: fields.userid, request_id: fields.request_id, navigation: navigateFlag }, connection)

        const creditLimitResp = await this.getCreditLimit(_, fields)
        log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtp', type: 'credit limit final resp', fields: creditLimitResp })

        return creditLimitResp
      }
      return verifyResp
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'verifyOtp', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }
  
  static async getRequestStatus(_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getRequestStatus', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
  
    // Request body validation
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid'])
    if (validateTransferParamRes.status != 200) return validateTransferParamRes
  
    // 
    try {
      const fetchCreditRequestQuery = `SELECT ma_advance_credit_requests_id ,approved_status FROM ma_advance_credit_requests WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} 
      AND addedon BETWEEN DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 3 MONTH) AND CURRENT_TIMESTAMP
      order by ma_advance_credit_requests_id  desc LIMIT 1`

      log.logger({ pagename: require('path').basename(__filename), action: 'getRequestStatus', type: 'fetch credit advance form data - query', fields: fetchCreditRequestQuery })
  
      const creditRequestResult = await this.rawQuery(fetchCreditRequestQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getRequestStatus', type: 'fetch credit advance form data - result', fields: creditRequestResult })

      if (!creditRequestResult.length) {
          return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, data:{request_id:null,show_btn: false, navigation:"SHOW_ALERT"} }
      }
    
      const createRequestStatus = creditRequestResult[0]?.approved_status?.toLowerCase()

    
      log.logger({ pagename: require('path').basename(__filename), action: 'getRequestStatus', type: 'createRequestStatus - value', fields: createRequestStatus});
    
      if (createRequestStatus === 'p') {
          return { status: 200, message: errorMsg.responseCode[12688],respcode: 1000, action_code: 1000 , data:{request_id:creditRequestResult[0]?.ma_advance_credit_requests_id, show_btn: false, navigation:null}};
      } 
      if (createRequestStatus === 'a') {
        const fetchCreditFormQuery = `SELECT navigation FROM ma_advance_credit_form_data WHERE ma_advance_credit_requests_id = ${creditRequestResult[0]?.ma_advance_credit_requests_id} `
        log.logger({ pagename: require('path').basename(__filename), action: 'getRequestStatus', type: 'fetch advance credit form details - query', fields: fetchCreditFormQuery })
    
        const fetchCreditFormResp = await this.rawQuery(fetchCreditFormQuery, connection)
  
        log.logger({ pagename: require('path').basename(__filename), action: 'getRequestStatus', type: 'fetch advance credit form details - result', fields: fetchCreditFormResp })

        const navigationVal = (fetchCreditFormResp[0]?.navigation == null || fetchCreditFormResp[0]?.navigation == "") ? displayFormSection.genInfo : fetchCreditFormResp[0]?.navigation

        const navigation =  (['OTP', 'GEN_INFO'].includes(navigationVal.toUpperCase())) ? displayFormSection.genInfo : navigationVal
        console.log("navigation Value", navigation)

        
        return {status: 200,message: errorMsg.responseCode[12689],respcode: 1000,action_code: 1000,data: { request_id: creditRequestResult[0]?.ma_advance_credit_requests_id || null, show_btn: true, navigation }
        };
       
      }
      if (createRequestStatus === 'r') {
        return { status: 200, message: errorMsg.responseCode[12693], respcode: 1000, action_code: 1000 ,  data:{request_id:creditRequestResult[0]?.ma_advance_credit_requests_id, show_btn: false, navigation:null}};
      }

      if (createRequestStatus === 'exp') {
        return { status: 200, message: errorMsg.responseCode[12695], respcode: 1000, action_code: 1000 ,  data:{request_id:creditRequestResult[0]?.ma_advance_credit_requests_id, show_btn: false, navigation:null}};
      }
  
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getRequestStatus', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }
  
  static async submitQuoteAmt(_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'submitQuoteAmt', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
  
    // Request body validation
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid', 'amount', 'request_id'])
    if (validateTransferParamRes.status != 200) return validateTransferParamRes
  
    try {
      const fetchCreditFormQuery = `SELECT navigation,avg_business_volume  FROM ma_advance_credit_form_data WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid}  AND ma_advance_credit_requests_id = ${fields.request_id}  order by ma_advance_credit_form_data_id desc LIMIT 1`

      log.logger({ pagename: require('path').basename(__filename), action: 'submitQuoteAmt', type: 'fetch credit form details - query', fields: fetchCreditFormQuery })
  
      const creditFormResult = await this.rawQuery(fetchCreditFormQuery, connection)  
      log.logger({ pagename: require('path').basename(__filename), action: 'submitQuoteAmt', type: 'fetch credit form details - result', fields: creditFormResult })
  
      if (creditFormResult.length <= 0) {
        return { status: 400, message: errorMsg.responseCode[1002], respcode: 1001, action_code: 1001 }
      }
      const avgAmount = creditFormResult[0]?.avg_business_volume
      const averageAmount = (avgAmount != '' && avgAmount != null) ?  parseFloat(avgAmount) : 0
      const requestedAmount = parseFloat(fields.amount)
      
      log.logger({ pagename: require('path').basename(__filename), action: 'submitQuoteAmt', type: 'Requested amount and average amount respectively', fields: {requestedAmount, averageAmount} })
  
  
      if (requestedAmount > averageAmount) {
        return { status: 400, message: errorMsg.responseCode[12690], respcode: 1001, action_code: 1001 }
      } else {
        const res = await this.getDocLink(_,fields);
        log.logger({ pagename: require('path').basename(__filename), action: 'submitQuoteAmt', type: 'getDocLink function - Response: ', fields: res })
        if (res.status == 400){
          return { status: 400, message: errorMsg.responseCode[1002], respcode: 1001, action_code: 1001 }
        }
        
        const updateRequestedAmtQry = `UPDATE ma_advance_credit_form_data SET requested_amount = ${fields.amount} WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_requests_id = ${fields.request_id} `
        
        await this.updateNavigation({ ma_user_id: fields.ma_user_id, userid: fields.userid,request_id:fields.request_id, navigation: displayFormSection.eSign }, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'submitQuoteAmt', type: 'update data - Sql: ', fields: updateRequestedAmtQry })
  
        const updateAmountResponse = await this.rawQuery(updateRequestedAmtQry, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'submitQuoteAmt', type: 'update data - Res: ', fields: updateAmountResponse })
        return { status: 200, message: errorMsg.responseCode[12691], respcode: 1000, action_code: 1000, data:res.data}
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'submitQuoteAmt', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async generateEsignRequest(_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'generateEsignRequest', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()

    // Request body validation
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid', 'doc_link', 'request_id', 'device_source'])
    if (validateTransferParamRes.status != 200) return validateTransferParamRes

    try {
      // fetch data
      const fetchFormQuery = `SELECT ma_advance_credit_form_data_id as form_id, ma_advance_credit_requests_id as request_id, ma_user_id, userid, firstname, lastname, email_id, mobile_number FROM ma_advance_credit_form_data WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_requests_id = ${fields.request_id} ORDER BY addedon DESC LIMIT 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'generateEsignRequest', type: 'fetch advance credit form - query', fields: fetchFormQuery })

      const fetchFormResult = await this.rawQuery(fetchFormQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'generateEsignRequest', type: 'fetch advance credit - result', fields: fetchFormResult })

      if (fetchFormResult.length <= 0) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }

      // prepare api data
      const FormData = require('form-data')
      let form = new FormData()

      const headers = {
        CHECKSUM: config.ESIGN_CHECKSUM,
        AFFILIATE: config.ESIGN_AFFILIATE,
        AIRPAYKEY: config.ESIGN_AIRPAYKEY,
        'Content-Type': 'application/x-www-form-urlencoded',
        ...form.getHeaders()
      }

      form.append('docrefid', config.esign_docrefid)
      form.append('email', fetchFormResult[0].email_id)
      form.append('doclink', fields.doc_link)
      form.append('mobile_no', fetchFormResult[0].mobile_number)
      form.append('first_name', fetchFormResult[0].firstname)
      form.append('last_name', fetchFormResult[0].lastname)

      const doclink = config.ESIGN_BASE_URL + fetchFormResult[0].form_id

      log.logger({ pagename: require('path').basename(__filename), action: 'generateEsignRequest', type: 'e-sign generate request data', fields: form })

      const response = await this.doApiCall({
        ma_user_id: fields.ma_user_id, 
        userid: fields.userid, 
        request_id: fields.request_id,
        api_name: fields.device_source.toLowerCase() == 'app' ? 'e_sign_generate_request_xml_api' : 'e_sign_generate_request_api',
        url: fields.device_source.toLowerCase() == 'app' ? config.esign_generate_req_xml_url : config.esign_generate_req_url,
        method: 'post',
        data: form,
        config: { headers }
      }, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'generateEsignRequest', type: 'e-sign generate request response', fields: response })

      if (response?.apiResponse?.status == 200 && response?.apiResponse?.status == 200) {
        const esignDocstring = fields.device_source.toLowerCase() == 'app' ? response?.apiResponse?.xml : response?.apiResponse?.url 
        
        // store doc link
        const updateRecordQuery = `UPDATE ma_advance_credit_form_data SET doc_link = '${esignDocstring}' WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_form_data_id = ${fetchFormResult[0].form_id}`
        log.logger({ pagename: require('path').basename(__filename), action: 'generateEsignRequest', type: 'fetch advance credit form - query', fields: updateRecordQuery })

        const updateRecordResult = await this.rawQuery(updateRecordQuery, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'generateEsignRequest', type: 'fetch advance credit - result', fields: updateRecordResult })

        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, data: { navigation: displayFormSection.eSign, request_id: fields.request_id, txn_id: response?.apiResponse?.txn_id || null, url: response?.apiResponse?.url || doclink, xml: response?.apiResponse?.xml || null} }
      }

      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'generateEsignRequest', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async generateEsignResponse(_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'generateEsignResponse', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()

    // Request body validation
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid', 'xml_data', 'request_id', 'device_source'])
    if (validateTransferParamRes.status != 200) return validateTransferParamRes

    try {
      const FormData = require('form-data')
      let form = new FormData()

      const headers = {
        CHECKSUM: config.ESIGN_CHECKSUM,
        AFFILIATE: config.ESIGN_AFFILIATE,
        AIRPAYKEY: config.ESIGN_AIRPAYKEY,
        'Content-Type': 'application/x-www-form-urlencoded',
        ...form.getHeaders()
      }

      form.append('xml_data', fields.xml_data)

      log.logger({ pagename: require('path').basename(__filename), action: 'generateEsignResponse', type: 'e-sign generate response xml api data', fields: form })

      const response = await this.doApiCall({
        ma_user_id: fields.ma_user_id, 
        userid: fields.userid, 
        request_id: fields.request_id,
        api_name: 'e_sign_generate_response_xml_api',
        url: config.esign_generate_res_xml_url,
        method: 'post',
        data: form,
        config: { headers }
      }, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'generateEsignResponse', type: 'e-sign generate response xml api response', fields: response })

      if (response?.apiResponse?.status == 200 && response?.apiResponse?.status == 200) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, data: { navigation: displayFormSection.eSign, request_id: fields.request_id, txn_id: response?.apiResponse?.txn_id || null, url: response?.apiResponse?.url || doclink, xml: response?.apiResponse?.xml || null} }
      }

      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'generateEsignResponse', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

      // calculate credit limit
  static async calculateCreditLimit(fields) {
    log.logger({pagename: require('path').basename(__filename),action: 'calculateCreditLimit',type: 'request',fields: fields,});
    const connection = await mySQLWrapper.getConnectionFromPool();
    try {
      let {merchant_type,avg_business_volume,credit_score} = fields;
      // Get max limit based on credit score from the database table
      const scoreBandQuery = `SELECT max_limit FROM ma_credit_score_range_master WHERE ${credit_score} BETWEEN min_score AND max_score AND record_status = 'Y' LIMIT 1`;
      log.logger({pagename: require('path').basename(__filename),action: 'calculateCreditLimit', type: 'score band query', fields: scoreBandQuery,});
      const scoreBandResult = await this.rawQuery(scoreBandQuery, connection);
      log.logger({pagename: require('path').basename(__filename),action: 'calculateCreditLimit',type: 'score band result',fields: scoreBandResult,});
      if (scoreBandResult.length == 0) {
        throw new Error('No score band found');
      }
      const maxLimit = scoreBandResult[0].max_limit;
      // query slabwise table to get % of max limit based on distributer type
      const slabQuery = `SELECT limit_percentage FROM ma_advanced_credit_linked_assessment WHERE user_type = '${merchant_type}' AND min_range <= ${avg_business_volume} AND max_range >= ${avg_business_volume} LIMIT 1`;
      log.logger({pagename: require('path').basename(__filename),action: 'calculateCreditLimit',type: 'slab query',fields: slabQuery,});
      const slabResult = await this.rawQuery(slabQuery, connection);
      if (slabResult.length == 0) {
        throw new Error('No slab found');
      }
      log.logger({pagename: require('path').basename(__filename),action: 'calculateCreditLimit',type: 'slab result',fields: slabResult,});
      const maxLimitPercent = slabResult[0].limit_percentage;
      console.log('maxLimit', maxLimit, 'maxLimitPercent', maxLimitPercent);
      // calculate the approved credit limit using formula - max limit * % of max limit / 100
      const approvedCreditLimit = (maxLimit * maxLimitPercent) / 100;
      // return the approved credit limit
      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000],
        data: { approvedCreditLimit },
      };
    } catch (error) {
      log.logger({pagename: require('path').basename(__filename),action: 'calculateCreditLimit',type: 'error',fields: error,});
      return {
        status: 400,
        respcode: 1001,
        message: error.message ||errorMsg.responseCode[1001],
        action_code: 1001,
      };
    } finally {
      connection.release();
    }
  }

  static async createCibilApiHeaders(CHECKSUM_KEY) {
    return {
      AIRPAYKEY: config.AIRPAYKEY,
      AFFILIATE: config.CIBILAFFILIATE,
      CHECKSUM: CHECKSUM_KEY,
      APIKEY: config.CIBILAPIKEY,
      'Content-Type': 'application/x-www-form-urlencoded',
    };
  }

  static async fetchCibilScore(fields) {
    const connection = await mySQLWrapper.getConnectionFromPool();
    log.logger({ pagename: require('path').basename(__filename), action: 'fetchCibilScore', type: 'fetch cibil score - request', fields });
    const { ma_user_id, userid, pan, request_id, email_id, mobile_number, address, personal_address, pincode, dob, gender, firstname, lastname } = fields;
    try {
      // fetch cities and state from ma_user_master
      const fetchCitiesAndStateDetailsQuery = `SELECT mcm.name as city, a.state, kyc_name FROM ma_user_master AS a JOIN ma_cities_master as mcm on mcm.id = a.city WHERE profileid = ${ma_user_id} AND userid = ${userid} LIMIT 1`;
      const merchantDetailsResult = await this.rawQuery(fetchCitiesAndStateDetailsQuery,connection);
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchCibilScore', type: 'fetch cities and state details - result', fields: merchantDetailsResult });
      // prepare request body for cibil enrollment api
      // let Email = email_id;
      // let PhoneNumber = mobile_number;
      // let Forename = firstname;
      // // if last name is empty then take the kyc string and split it by space and take the last word as last name
      // let Surname = lastname ||  merchantDetailsResult[0].kyc_name.split(' ').pop();
      // let IdentificationId = pan;
      // let StreetAddress = personal_address;
      // let PostalCode = pincode;
      // let City = merchantDetailsResult[0].city;
      // let Region = merchantDetailsResult[0].state;
      // let DateOfBirth = merchantDetailsResult[0].dob || '1990-01-01';
      // let Gender = gender || 'Male';

      // dummy data
      let Email = '<EMAIL>';
      let PhoneNumber = '8195955106';
      let Forename = 'Pankaj';
      let Surname = 'Karmakar';
      let IdentificationId = '**********';
      let StreetAddress = 'House No 14, Navi Mumbai';
      let PostalCode = '400708';
      let City = 'Navi Mumbai';
      let Region = '27';
      let DateOfBirth = '1990-08-17';
      let Gender = 'Male';

      const cibilEnrollmentRequestBody = { apiType : config.CIBILENROLLMENTAPITYPE, PartnerCustomerId: config.CIBILPARTNERCUSTOMERID, Email, PhoneNumber, Forename, Surname, IdentificationId, StreetAddress, PostalCode, City, Region, DateOfBirth, Gender };
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchCibilScore', type: 'cibil enrollment api request body', fields: { apiType : config.CIBILENROLLMENTAPITYPE, PartnerCustomerId: config.CIBILPARTNERCUSTOMERID, Email, PhoneNumber, Forename, Surname, IdentificationId, StreetAddress, PostalCode, City, Region, DateOfBirth, Gender } });

      // make a post request to cibil api
      const cibilenrollmentData = qs.stringify(cibilEnrollmentRequestBody);
      const CHECKSUM_KEY = await this.generateChecksum(cibilEnrollmentRequestBody,config.IFSC_CHECKSUM_SECRETKEY);
      const cibilEnrollmentHeaders = await this.createCibilApiHeaders(CHECKSUM_KEY);
      const response = await this.doApiCall({ api_name: 'cibil_enrollment_api', url: config.CIBILURL, method: 'post', ma_user_id, request_id, userid, data: cibilenrollmentData, config: { headers: cibilEnrollmentHeaders } }, connection);
      const cibilEnrollmentResult = response.apiResponse.data.data;
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchCibilScore', type: 'cibil enrollment api response', fields: cibilEnrollmentResult });
      if (cibilEnrollmentResult?.FulfillOfferResponse?.FulfillOfferError) {
        log.logger({ pagename: require('path').basename(__filename), action: 'fetchCibilScore', type: 'cibil enrollment failed', fields: cibilEnrollmentResult?.FulfillOfferResponse?.FulfillOfferError?.Failure });
        return {
          status: 400,
          message: 'Fail: CIBIL Enrollment Failed',
          respcode: 1001,
          action_code: 1001,
          data: cibilEnrollmentResult?.FulfillOfferResponse?.FulfillOfferError
            .Failure,
        };
      }
      // Check for 'InProgress' status in the success block
      if (cibilEnrollmentResult?.FulfillOfferResponse?.FulfillOfferSuccess?.Status === 'InProgress') {
        log.logger({ pagename: require('path').basename(__filename), action: 'fetchCibilScore', type: 'cibil enrollment status InProgress', fields: cibilEnrollmentResult.FulfillOfferResponse.FulfillOfferSuccess });
        return {
          status: 404,
          message: 'Fail: CIBIL Enrollment InProgress',
          respcode: 1001,
          action_code: 1001,
          data: cibilEnrollmentResult?.FulfillOfferResponse?.FulfillOfferSuccess,
        };
      }

      if (cibilEnrollmentResult?.FulfillOfferResponse?.FulfillOfferSuccess?.Status === 'Success') {
        log.logger({pagename: require('path').basename(__filename),action: 'fetchCibilScore',type: 'cibil enrollment status Success, proceeding to fetch score', fields: cibilEnrollmentResult});
        const getCibilBody = {apiType: config.CIBILCUSTOMERAPITYPE, PartnerCustomerId: config.CIBILPARTNERCUSTOMERID};
        const cibildata = qs.stringify(getCibilBody);
        const CIBIL_CHECKSUM_KEY = await this.generateChecksum(getCibilBody,config.IFSC_CHECKSUM_SECRETKEY,);
        const cibilScoreheaders = await this.createCibilApiHeaders(CIBIL_CHECKSUM_KEY)
        const cibilResponse = await this.doApiCall(
          {
            api_name: 'cibil_score_api',
            url: config.CIBILURL,
            method: 'post',
            data: cibildata,
            ma_user_id,
            request_id, 
            userid,
            config: { headers: cibilScoreheaders },
          },
          connection,
        );
        const cibilResult = cibilResponse.apiResponse.data?.data;
        log.logger({pagename: require('path').basename(__filename),action: 'fetchCibilScore',type: 'cibil score api response',fields: cibilResult,});
        console.log(
          'cibilScoreResult',
          cibilResult.GetCustomerAssetsResponse.GetCustomerAssetsError,
        );
        // --- Extract and return CIBIL Score ---
        const cibilScore = cibilResult?.GetCustomerAssetsResponse?.GetCustomerAssetsSuccess?.Asset?.TrueLinkCreditReport?.Borrower?.CreditScore?.riskScore;
        if (cibilScore) {
          log.logger({pagename: require('path').basename(__filename),action: 'fetchCibilScore',type: 'cibil score extracted successfully',fields: { cibilScore },
          });
          // Score found, return success with the score
          return {
            status: 200,
            message: 'Success',
            respcode: 1000,
            data: {
              cibilScore: parseInt(cibilScore, 10), // Parse score as integer if it's a string
            },
          };
        } else {
          log.logger({pagename: require('path').basename(__filename),action: 'fetchCibilScore',type: 'cibil score not found in response',fields: cibilResult,
          });
          // Score not found in the expected path of the GetCustomerAssets response
          return {
            status: 400,
            message: 'Fail: CIBIL Score not found in response structure',
            respcode: 1001,
            action_code: 1001,
            data: cibilResult,
          };
        }
      }
    } catch (error) {
      log.logger({
        pagename: require('path').basename(__filename),action: 'fetchCibilScore',type: 'error',fields: error,});
      return {
        status: 400,
        respcode: 1001,
        message: errorMsg.responseCode[1001],
        action_code: 1001,
      };
    } finally {
      connection.release();
    }
  }

  static async fetchInterestRateAndPenaltyRate(fields) {
    const connection = await mySQLWrapper.getConnectionFromPool();
    const { ma_user_id, user_type } = fields;
    try {
      // Check if data exist for the given distributer user type, if yes then return the rates
      const fetchInterestRateQuery = `SELECT interest_rate, penality_rate, penality_applied_type FROM ma_advance_credit_settings WHERE ma_user_id = ${ma_user_id} AND user_type = '${user_type}' LIMIT 1`;
      const interestRateResult = await this.rawQuery(fetchInterestRateQuery,connection,);
      if (interestRateResult.length > 0) {
        const interestRate = interestRateResult[0].interest_rate;
        const penaltyRate = interestRateResult[0].penality_rate;
        const penalityAppliedType = interestRateResult[0].penality_applied_type;
        log.logger({pagename: require('path').basename(__filename),action: 'fetchInterestRateAndPenaltyRate',type: 'interest rate and penalty rate found for user type',fields: { interestRate, penaltyRate, penalityAppliedType }});
        return { status: 200, interestRate, penaltyRate, penalityAppliedType };
      } else {
        // If no data then, fetch state_master_id for ma_user_master ma_user_master - state ->id
        const fetchStateMasterQuery = `SELECT state FROM ma_user_master WHERE profileid = ${ma_user_id} LIMIT 1`;
        const stateMasterResult = await this.rawQuery(fetchStateMasterQuery,connection,);
        if (stateMasterResult.length > 0) {
          const stateMasterId = stateMasterResult[0].state;
          const fetchInterestandPenaltyQuery = `SELECT interest_rate, penality_rate, penality_applied_type FROM ma_advance_credit_settings WHERE state_master_id = ${stateMasterId} LIMIT 1`;
          const interestandPenaltyResult = await this.rawQuery(fetchInterestandPenaltyQuery,connection,);
          if (interestandPenaltyResult.length > 0) {
            log.logger({pagename: require('path').basename(__filename),action: 'fetchInterestRateAndPenaltyRate',type: 'interest rate and penalty rate found for state',fields: interestandPenaltyResult[0],});
            return {
              status: 200,
              interestRate: interestandPenaltyResult[0].interest_rate,
              penaltyRate: interestandPenaltyResult[0].penality_rate,
              penalityAppliedType:
                interestandPenaltyResult[0].penality_applied_type,
            };
          }
        }
        // If no data then, fetch global data 
        const fetchInterestandPenaltyQuery = `SELECT interest_rate, penality_rate, penality_applied_type FROM ma_advance_credit_settings WHERE settings_type = 'G' LIMIT 1`;
        const interestandPenaltyResult = await this.rawQuery(fetchInterestandPenaltyQuery,connection,);
        if (interestandPenaltyResult.length > 0) {
          log.logger({pagename: require('path').basename(__filename),action: 'fetchInterestRateAndPenaltyRate',type: 'interest rate and penalty rate found for global',fields: interestandPenaltyResult[0],});
          return {
            status: 200,
            interestRate: interestandPenaltyResult[0].interest_rate,
            penaltyRate: interestandPenaltyResult[0].penality_rate,
            penalityAppliedType:
              interestandPenaltyResult[0].penality_applied_type,
          };
        } else {
          throw new Error('No interest rate and penalty rate found');
        }
        
      }
    } catch (error) {
      log.logger({pagename: require('path').basename(__filename),action: 'fetchInterestRateAndPenaltyRate',type: 'error', fields: error,});
      return {
        status: 400,
        respcode: 1001,
        message: errorMsg.responseCode[1001],
        action_code: 1001,
      };
    } finally {
      connection.release();
    }
  }

  static async getCreditLimit(_, fields) {
    log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit', type: 'getCreditLimit - gql query request',fields: fields});
    const connection = await mySQLWrapper.getConnectionFromPool();
    // Request body validation
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id','userid', 'request_id']);
    if (validateTransferParamRes.status != 200) return validateTransferParamRes;
    try {
      // fetch pan and merchant type from ma_advance_credit_form_data
      const fetchMerchantDetailsQuery = `SELECT ma_user_id, userid, email_id, mobile_number, merchant_type, CAST(AES_DECRYPT(pan,'${config.decryptionKey}') AS CHAR) as pan, address, personal_address, pincode, dob, firstname, lastname, gender FROM ma_advance_credit_form_data WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_requests_id = ${fields.request_id} AND form_approval_status = 'P'  LIMIT 1`
      log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'fetch merchant pan and merchant type - sql query',fields: fetchMerchantDetailsQuery});
      const merchantDetailsResult = await this.rawQuery(fetchMerchantDetailsQuery,connection);
      log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'fetch merchant pan and merchant type - sql result',fields: merchantDetailsResult});
      if(merchantDetailsResult.length == 0){
        throw new Error('No merchant details found');
      }
      // fetch avg_business_volume from ma_advance_credit_requests
      const fetchAvgBusinessVolumeQuery = `SELECT avg_business_volume FROM ma_advance_credit_requests WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND approved_status = 'A' AND ma_advance_credit_requests_id = ${fields.request_id} LIMIT 1`
      log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'fetch avg business volume - sql query',fields: fetchAvgBusinessVolumeQuery});
      const fetchAvgBusinessVolumeResult = await this.rawQuery(fetchAvgBusinessVolumeQuery,connection);
      log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'fetch avg business volume - sql result',fields: fetchAvgBusinessVolumeResult});
      const avg_business_volume = fetchAvgBusinessVolumeResult[0]?.avg_business_volume || 0;
      const fetchCibilScoreObj = {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        pan: merchantDetailsResult[0].pan,
        request_id: fields.request_id,
        email_id: merchantDetailsResult[0].email_id,
        mobile_number: merchantDetailsResult[0].mobile_number,
        address: merchantDetailsResult[0].address,
        personal_address: merchantDetailsResult[0].personal_address,
        pincode: merchantDetailsResult[0].pincode,
        dob: merchantDetailsResult[0].dob,
        gender: merchantDetailsResult[0].gender,
        firstname: merchantDetailsResult[0].firstname,
        lastname: merchantDetailsResult[0].lastname,
      }
      const pan = merchantDetailsResult[0].pan;
      const merchantType = merchantDetailsResult[0].merchant_type;
      const avgBusinessVolume = avg_business_volume;
      // hard pull of cibil score
      // const cibilScoreResponse = await this.fetchCibilScore(fetchCibilScoreObj);
      const cibilScoreResponse = {
        status: 200,
        message: 'Success',
        respcode: 1000,
        data: {
          cibilScore: Math.floor(Math.random() * (900 - 650 + 1)) + 650
        },
      };
      log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'final fetch cibil score - response',fields:cibilScoreResponse});

      // if cibil score is positive calculate credit limit
      if (cibilScoreResponse.status === 200) {
        const cibilScore = cibilScoreResponse.data.cibilScore;
        // prepare args object for calculateCreditLimit
        const args = {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          merchant_type: merchantType,
          avg_business_volume: avgBusinessVolume,
          credit_score: cibilScore,
          pan: pan,
        };
        const creditLimitResp = await this.calculateCreditLimit(args);
        if (creditLimitResp.status == 200) {
          const { approvedCreditLimit } = creditLimitResp.data;
          // fetch interest rate and penalty rate from admin
          let interestRate;
          let penaltyRate;
          let penalityAppliedType;
          let appliedPenaltyType;
          const ratesResponse = await this.fetchInterestRateAndPenaltyRate({ma_user_id: fields.ma_user_id,user_type: merchantType});
          log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'fetch interest rate and penalty rate - response',fields: ratesResponse,});
          console.log("ratesResponse", ratesResponse);
          if (ratesResponse?.status == 200) {
            interestRate = ratesResponse.interestRate;
            penaltyRate = ratesResponse.penaltyRate;
            penalityAppliedType = ratesResponse.penalityAppliedType;
            if(penalityAppliedType === '1') {
              appliedPenaltyType = 'Fixed';
            } else {
              appliedPenaltyType = 'Percent';
            }
          } else {
            throw new Error('FAIL: Failed to fetch interest rate and penalty rate');
          }
          console.log('interestRate', interestRate, 'penaltyRate', penaltyRate, 'penalityAppliedType', penalityAppliedType);
          // Insert or update the credit limit details in ma_advance_credit_form_data table
          const checkExistingQuery = `SELECT * FROM ma_advance_credit_form_data WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_requests_id = ${fields.request_id} LIMIT 1`;
          const existingRecord = await this.rawQuery(checkExistingQuery,connection);
          let updateQuery;
          if (existingRecord.length > 0) {
            console.log("ffd", avgBusinessVolume)
            // Update existing record
            updateQuery = `UPDATE ma_advance_credit_form_data SET quotation_amount = ${approvedCreditLimit.toFixed(2)}, 
            avg_business_volume = ${avgBusinessVolume.toFixed(2)} , cibil_score = ${cibilScore} WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_requests_id = ${fields.request_id}`;
          }
          log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'update credit limit details in ma_advance_credit_form_data - query',fields: updateQuery,});
          const updateResult = await this.rawQuery(updateQuery, connection);
          log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'update credit limit details in ma_advance_credit_form_data - result',fields: updateResult,});
          // log final response and return
          log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'getCreditLimit - gql final response',fields: { approvedCreditLimit, interestRate, penaltyRate, appliedPenaltyType },});
          return {
            status: 200,
            respcode: 1000,
            message: errorMsg.responseCode[1000],
            data: { approvedCreditLimit, interestRate, penaltyRate, appliedPenaltyType, request_id: fields.request_id, navigation: displayFormSection.credQuote },
            action_code: 1000,
          };
        }
        log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'error - calculateCreditLimit',fields: creditLimitResp,
        });
        return {
          status: 400,
          respcode: 1001,
          message: creditLimitResp.message,
          action_code: 1001,
          data: {
            request_id: fields.request_id,
            navigation: displayFormSection.credQuote
          }
        };
      } else if (cibilScoreResponse.status == 404) {
        log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'Negative cibilScore Reponse error',fields: cibilScoreResponse,});
        // update form data approval status
        const updateFormDataApprovalStatus = `UPDATE ma_advance_credit_form_data SET form_approval_status = 'R' WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_requests_id = ${fields.request_id}`;
        const updateFormDataApprovalStatusResult = await this.rawQuery(updateFormDataApprovalStatus, connection);
        log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'update form data approval status - result',fields: updateFormDataApprovalStatusResult,});
        // update navigation
        const navigateFlag = displayFormSection.credStatus
        const updateNavResult = await this.updateNavigation({ ma_user_id: fields.ma_user_id, userid: fields.userid, request_id: fields.request_id, navigation: navigateFlag }, connection)
        log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'update navigation - result',fields: navigateFlag,});
        // check if update navigation is not successful
        if(updateNavResult.status != 200){
          log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'error - update navigation',fields: updateNavResult,});
          return updateNavResult;
        }
        // else return success response
        log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'Negative cibilScore Reponse - gql final response',fields: { status: cibilScoreResponse.data?.Status, request_id: fields.request_id, navigation: navigateFlag },});
        return {
          status: 200,
          respcode: 1001,
          message: 'CIBIL Enrollment Failed.',
          action_code: 1001,
          data: {
            // status: cibilScoreResponse.data?.Status,
            request_id: fields.request_id,
            navigation: navigateFlag
          },
        };
 
      } else {
        log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'general cibilScoreResponse error',fields: cibilScoreResponse,});
        return {
          status: 400,
          respcode: 1001,
          message: cibilScoreResponse.data.Message ? cibilScoreResponse.data.Message : errorMsg.responseCode[1001],
          action_code: 1001,
          data: {
            request_id: fields.request_id,
            navigation: displayFormSection.credQuote
          }
        };
      }
    } catch (error) {
      log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'general error',fields:error,});
      return {
        status: 400,
        respcode: 1001,
        message: error.message || errorMsg.responseCode[1001],
        action_code: 1001,
        data: {
          request_id: fields.request_id,
          navigation: displayFormSection.credQuote
        }
      };
    } finally {
      connection.release();
      log.logger({pagename: require('path').basename(__filename),action: 'getCreditLimit',type: 'finally',fields: 'connection released',});
    }
  }

  static async submitSignedAgreement (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'submitSignedAgreement', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()

    // Request body validation
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid', 'signed_doc_link', 'request_id'])
    if (validateTransferParamRes.status != 200) return validateTransferParamRes

    try {
      const updateDataQuery = `UPDATE ma_advance_credit_form_data SET signed_agreement_link = '${fields.signed_doc_link}', navigation = '${displayFormSection.credStatus}', form_approval_status = 'OP' WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${fields.userid} AND ma_advance_credit_requests_id = ${fields.request_id}`
      log.logger({ pagename: require('path').basename(__filename), action: 'submitSignedAgreement', type: 'update data - query', fields: updateDataQuery })

      const updateDataResult = await this.rawQuery(updateDataQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'submitSignedAgreement', type: 'update data - result', fields: updateDataResult })
      
      if (updateDataResult.affectedRows > 0) {
        return await this.getFormApprovalStatus (_, fields)
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'submitSignedAgreement', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }
  
  static async getFormApprovalStatus (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getFormApprovalStatus', type: 'request', fields: fields })
    try {
      // check existing form record
      const formDetails = await this.fetchFormDetails(fields)
      log.logger({ pagename: require('path').basename(__filename), action: 'getFormApprovalStatus', type: 'fetch advance credit form details', fields: formDetails })

      let formStatusResponse = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      let messageTitle
      let messageDesc

      if (formDetails.length === 0) return formStatusResponse

      if (formDetails[0].form_approval_status == 'P') {
        return { status: 400, message: 'Fail: Incomplete form', respcode: 1001, action_code: 1001 }
      } else if (formDetails[0].form_approval_status == 'A') {
        messageTitle = `Congratulations ${formDetails[0]?.firstname} !!! Your daily credit limit of ${formDetails[0]?.requested_amount} INR has been approved.`
        messageDesc = `Click on proceed to complete to sanction the credit in your trading balance`
      } else if (formDetails[0].form_approval_status == 'R'|| formDetails[0].form_approval_status == 'OR') {
        messageTitle = 'Unfortunately, you are not eligible for Advance Credit Approval.'
        messageDesc = ''
      } else if (formDetails[0].form_approval_status == 'OP') {
        messageTitle = `Please wait, while we review it.`
        messageDesc = `Please check this page regularly for updates. Any new information will be posted here.`
      } else {
        return formStatusResponse
      }

      formStatusResponse = { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, data: { form_approval_status: formDetails[0].form_approval_status, messageTitle, messageDesc, navigation: displayFormSection.credStatus } }

      return formStatusResponse
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getFormApprovalStatus', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  
  static async getDocLink(_,data){
    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      const fetchCreditFormQuery = `SELECT ma_advance_credit_form_data_id as form_id from ma_advance_credit_form_data WHERE ma_user_id = ${data.ma_user_id} AND userid = ${data.userid} AND ma_advance_credit_requests_id = ${data.request_id} LIMIT 1`;
      log.logger({ pagename: require('path').basename(__filename), action: 'getDocLink', type: 'fetch merchant details - query', fields: fetchCreditFormQuery });
  
      const advanceFormResult = await this.rawQuery(fetchCreditFormQuery,connection);
      log.logger({ pagename: require('path').basename(__filename), action: 'getDocLink', type: 'fetch merchant details - result', fields: advanceFormResult });
  
      // data.email_id = advanceFormResult[0]?.email_id?? ""
      // data.mobile_number = advanceFormResult[0]?.mobile_number ?? ""
      // data.firstname = advanceFormResult[0]?.firstname ?? ""
      // data.lastname = advanceFormResult[0]?.lastname ?? ""
      

      const doclink = config.ESIGN_BASE_URL + advanceFormResult[0]?.form_id
  
      log.logger({ pagename: require('path').basename(__filename), action: 'getDocLink', type: 'Fields data after adding merchant details', fields: data })
     
     
      const updateDataQuery = `UPDATE ma_advance_credit_form_data SET doc_link = '${doclink}' WHERE ma_user_id = ${data.ma_user_id} AND userid = ${data.userid} AND ma_advance_credit_requests_id = ${data.request_id}`
    
      const updateDataResult = await this.rawQuery(updateDataQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getDocLink', type: 'update navigation - result', fields: updateDataResult })

      if (updateDataResult.affectedRows > 0) {
        // const generateReqResponse = await this.generateEsignRequest("",data)
        // log.logger({ pagename: require('path').basename(__filename), action: 'getDocLink', type: 'generateEsignRequest- response', fields: generateReqResponse })

        // if(generateReqResponse.status != 200 && generateReqResponse.apiResponse.status != 200){
          // return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
        // }
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, data:{url:doclink, navigation:displayFormSection.eSign, request_id:data.request_id} }

      }
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
     

    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getDocLink', type: 'error', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }finally {
      connection.release()
    }
  }
}

module.exports = advancecreditController;
