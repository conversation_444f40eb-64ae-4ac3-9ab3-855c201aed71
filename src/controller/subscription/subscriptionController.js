const DAO = require('../../lib/dao')
const securePinCtrl = require('../securityPin/securityPinController')
const errorMsg = require('../../util/error')
const validator = require('../../util/validator')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const log = require('../../util/log')
const path = require('path')
const BalanceController = require('../balance/balanceController')
const errorEmail = require('../../util/errorHandler')
const TransactionController = require('../transaction/transactionController')
const commonFunctionController = require('../common/commonFunctionController')
const util = require('../../util/util')
const MySQLWrapper = require('../../lib/mysqlWrapper')
const lienBalanceController = require('../lienBalance/lienBalanceController')
const moment1 = require('moment')

const common = require('../../util/common')
const axios = require('axios')
const moment = require('moment')
const tz = require('moment-timezone')

class SubscriptionController extends DAO {
  /**
   * Fetches Utility Details from Database
   * <AUTHOR> @returns {Promise <{ key: number, value: string, web_icon: string, app_icon: string }>}
   */
  static async getPlansList (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'getPlansList', fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      // Validate mandatory fields
      const validationResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'module_type'])
      if (validationResponse.status !== 200) return validationResponse

      // check user type
      const user_type_query = `select user_type from ma_user_master where profileid=${fields.ma_user_id} and userid=${fields.userid} limit 1`
      const user_type_res = await this.rawQuery(user_type_query, connection)
      log.logger({ pagename: path.basename(__filename), action: 'getPlansList', message: 'check user type', user_type_res })

      const check_plans_query = `select ma_subscription_plan_id, plan_duration, short_description, long_description, price, module_type, incentive_share, incentive_share_applied_type from ma_subscription_plans msp where module_type='${fields.module_type}' and user_type in ('${user_type_res[0].user_type}', 'ALL') and plan_status='Y'`

      const check_plans_resp = await this.rawQuery(check_plans_query, connection)
      log.logger({ pagename: path.basename(__filename), action: 'getPlansList', type: 'check_plans_resp', fields: check_plans_resp })

      if (!check_plans_resp.length) {
        return { status: 400, respcode: 1001, message: 'Plans not available', action_code: 1001, planList: check_plans_resp }
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], planList: check_plans_resp }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'getPlansList', type: 'err', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async showButton (expiryTime) {
    try {
      // Parse expiryTime into IST
      const expiry = moment(expiryTime).tz('Asia/Kolkata').startOf('day')

      // Get today in IST
      const today = moment().tz('Asia/Kolkata').startOf('day')

      console.log('expiry', expiry.format())
      console.log('today', today.format())

      const daysLeft = expiry.diff(today, 'days')
      console.log('daysLeft', daysLeft)
      
      return daysLeft <= 10
      // const currentDate = moment().tz('Asia/Kolkata').startOf('day')

      // // Convert expiryDate to moment object in IST timezone
      // const expiry = moment(expiryDate, 'DD-MM-YYYY').startOf('day')
      // console.log('expiry', expiry.format('YYYY-MM-DD'))

      // // Calculate the difference in days between expiry date and current date
      // const daysUntilExpiry = expiry.diff(currentDate, 'days')
      // console.log('daysUntilExpiry', daysUntilExpiry)
      // console.log('expiry time', expiryTime)
      // // Show button if current date is 10 days or less before expiry date
      // return daysUntilExpiry <= expiryTime
      // return true
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'showButton', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  static async getMerchantSubscriptionStatus (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantSubscriptionStatus', type: 'getMerchantSubscriptionStatus', fields })
    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      // Validate input fields
      const validationResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'module_type'])

      if (validationResponse.status != 200) return validationResponse
      
      const checkActivePlansResult = await this.checkActivePlans(fields, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantSubscriptionStatus', type: 'checkActivePlansResult', fields: checkActivePlansResult })
      if (checkActivePlansResult.status == 400) return checkActivePlansResult
      if (checkActivePlansResult) {
        const expirtyTime = await common.getSystemCodes(this, util.subscriptionExpiryTime, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantSubscriptionStatus', type: 'expirtyTime', fields: expirtyTime })
        const showButtonStatus = await this.showButton(checkActivePlansResult[0].endDate)
        log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantSubscriptionStatus', type: 'showButtonStatus', fields: showButtonStatus })
        if (showButtonStatus.status == 400) return showButtonStatus
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, showRenewButton: showButtonStatus, planList: [], activePlans: checkActivePlansResult }
      } else {
        const getPlanListResult = await this.getPlansList('_', fields)
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, showRenewButton: false, planList: getPlanListResult.planList, activePlans: [] }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantSubscriptionStatus', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  static async checkActivePlans (fields, connection) {
    try {
      const activePlanSql = `Select msm.ma_subscription_plan_id, msm.plan_duration,msm.short_description, msm.long_description, msm.price,msm.module_type, msm.incentive_share, msm.incentive_share_applied_type, DATE_FORMAT(mms.plan_start_date, "%d-%m-%Y") as startDate,plan_end_date as endDate, mms.subscription_status from ma_merchant_subscription mms left join ma_subscription_plans msm on mms.ma_subscription_plan_id = msm.ma_subscription_plan_id  where mms.ma_user_id = ${fields.ma_user_id} and mms.user_id =  ${fields.userid} and mms.module_type = '${fields.module_type}' and mms.subscription_status = 'A' AND mms.plan_end_date >= NOW() order by mms.added_on desc limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'checkActivePlans', type: 'activePlanSql', fields: activePlanSql })

      const activePlanResult = await this.rawQuery(activePlanSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'checkActivePlans', type: 'activePlanResult', fields: activePlanResult })

      if (activePlanResult.length == 0) return false

      return activePlanResult
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'showButton', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    }
  }

  static async confirmSubscription (_, fields) {
    log.logger({ pagename: path.basename(__filename), action: 'confirmSubscription', type: 'request', fields: fields })

    const connection = await mySQLWrapper.getConnectionFromPool()
    const connectionRead = await mySQLWrapper.getConnectionFromReadReplica()

    const validatorResponse = validator.validateFields(fields, ['ma_user_id', 'userid', 'aggregator_order_id', 'amount', 'pin', 'module_type', 'ma_subscription_plan_id'])
    if (validatorResponse.status != 200) return validatorResponse
    try {
      const { ma_user_id, userid, aggregator_order_id, amount, pin, module_type } = fields

      const sqlQuery = `SELECT plan_end_date ,module_type from  ma_merchant_subscription WHERE ma_user_id=${fields.ma_user_id} AND user_id=${fields.userid} AND subscription_status='A' AND module_type= '${fields.module_type}'  `

      const planExpDate = await this.rawQuery(sqlQuery, connection)
      let canRenew = false
      if (planExpDate.length != 0) {
        const currentDate = new Date()
        const endDate = planExpDate[0].plan_end_date
        const tenDaysBeforeEndDate = new Date(endDate)
        tenDaysBeforeEndDate.setDate(endDate.getDate() - 10)
        const moduleType = planExpDate[0].module_type.toLowerCase()
        const fieldsModuleType = fields.module_type.toLowerCase()
        if (currentDate < tenDaysBeforeEndDate && fieldsModuleType === moduleType) {
          canRenew = true
          return { status: 200, respcode: 1000, message: 'You have already active subscription plan' }
        }
      }

      // Merchant Details
      if (!canRenew) {
        const merchant = await this.getMerchantDetail(ma_user_id, userid, connectionRead)
        if (merchant.status != 200) return merchant

        fields.merchant_mobile_number = merchant.mobile_number
        fields.merchant_name = merchant.merchant_name
        // Verify Pin
        const securePin = await securePinCtrl.verifySecurePin(null, {
          ma_user_id,
          userid,
          security_pin: pin,
          connection
        })

        log.logger({ pagename: path.basename(__filename), action: 'confirmSubscription', type: 'securePin', fields: securePin })
        if (securePin.status != 200) return securePin

        // Check Available
        const availableBalance = await BalanceController.getWalletBalancesDirect('_', {
          ma_user_id: ma_user_id,
          ma_status: 'ACTUAL',
          balance_flag: 'SUMMARY',
          connection: connectionRead
        })
        log.logger({ pagename: path.basename(__filename), action: 'confirmSubscription', type: 'availableBalance', fields: availableBalance })
        if (availableBalance.amount < fields.amount) return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005], action_code: 1001 }

        /* NEW CHANGES : LEIN BALANCE CHECK */
        const isTransactionAmountAboveLienBalanceResp = await lienBalanceController.isTransactionAmountAboveLienBalance({
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          transactionType: '86',
          transactionAmount: parseFloat(fields.amount),
          connectionRead: connection
        })
        log.logger({ pagename: 'subscriptionController.js', action: 'confirmSubscription', type: 'response', fields: isTransactionAmountAboveLienBalanceResp })
        if (isTransactionAmountAboveLienBalanceResp.status != 200) return isTransactionAmountAboveLienBalanceResp

        const sql = `SELECT plan_duration FROM ma_subscription_plans WHERE ma_subscription_plan_id = ${fields.ma_subscription_plan_id} AND plan_status='Y' `

        const planDuration = await this.rawQuery(sql, connection)
        log.logger({ pagename: 'subscriptionController.js', action: 'confirmSubscription', type: 'response', fields: planDuration })
        if (planDuration.length === 0) {
          return { status: 400, respcode: 1000, message: 'Subscription plan not found' }
        }
        fields.plan_duration = planDuration[0].plan_duration

        const initTransaction = await this.initiateTransaction({ fields: { ...fields, transaction_type: '86' }, connection, connectionRead })
        if (initTransaction.status != 200) {
          return initTransaction
        }

        const ledgerEntriesResp = await this.ledgerEntries(_, fields)
        log.logger({ pagename: require('path').basename(__filename), action: 'confirmSubscription', type: 'ledgerEntriesResp', fields: ledgerEntriesResp })
        if (ledgerEntriesResp.status === 400) {
        // ----- SEND TRANSFER FAILURE MESSAGE
          await MySQLWrapper.rollback(connection)
          return ledgerEntriesResp
        }
        let startDatetime
        let endDate

        const totalDays = fields.plan_duration * 30 // Assuming plan duration is in months
        console.log('fields.plan_duration:', fields.plan_duration)

        if (planExpDate.length === 0) {
          const start = new Date()
          start.setDate(start.getDate() + 1)
          startDatetime = start.toISOString().slice(0, 10)

          const end = new Date(start)
          end.setDate(end.getDate() + totalDays - 1)
          endDate = end.toISOString().substring(0, 10)
        } else {
          const lastEnd = new Date(planExpDate[0].plan_end_date)
          lastEnd.setDate(lastEnd.getDate() + 1)
          startDatetime = lastEnd.toISOString().slice(0, 10)

          const end = new Date(lastEnd)
          end.setDate(end.getDate() + totalDays - 1)
          endDate = end.toISOString().substring(0, 10)
        }
        const startDate = `${startDatetime} 00:00:00`
        const endDateTime = `${endDate} 23:59:59`

        const insertSqlData = `INSERT INTO ma_merchant_subscription ( ma_user_id, user_id, ma_subscription_plan_id, module_type,order_id ,plan_start_date,plan_end_date,subscription_status)
VALUES( ${fields.ma_user_id}, ${fields.userid}, ${fields.ma_subscription_plan_id}, '${fields.module_type}','${fields.aggregator_order_id}','${startDate}','${endDateTime}','A'); `
        const insertTransactions = await this.rawQuery(insertSqlData, connection)
        log.logger({ pagename: 'subscriptionController.js', action: 'confirmSubscription', type: 'response', fields: insertTransactions })

        if (insertTransactions.affectedRows === 0) {
          return insertTransactions
        }
        return { status: 200, respcode: 1000, message: 'Thank you! You have successfully purchased the subscription', aggregator_order_id: aggregator_order_id }
      }
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'confirmSubscription', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'confirmSubscription',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
      connectionRead.release()
    }
  }

  static async getMerchantDetail (ma_user_id, userid, conn) {
    const tempConnection = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    try {
      var userDetails = ''
      let merchant_name = ''
      let address = ''
      let mobile_number
      const sql = `SELECT CONCAT(firstname, ' ', lastname) AS merchant_name, address, mobile_id as mobile_number FROM ma_user_master WHERE profileid = ${ma_user_id} and userid = ${userid} and mer_user = 'mer' limit 1 `
      userDetails = await this.rawQuery(sql, connection)
      if (userDetails.length > 0) {
        merchant_name = userDetails[0].merchant_name
        address = userDetails[0].address
        mobile_number = userDetails[0].mobile_number
        return { status: 200, merchant_name: merchant_name, address: address, mobile_number }
      } else {
        return { status: 400, message: errorMsg.responseCode[1003], respcode: 1003 }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getMerchantDetail', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async initiateTransaction ({ fields, connection, connectionRead }) {
    log.logger({ pagename: path.basename(__filename), action: 'initiateTransaction', type: 'request', fields: fields })

    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    const isSetRead = (connectionRead === null || connectionRead === undefined)
    const connRead = isSetRead ? await mySQLWrapper.getConnectionFromReadReplica() : connectionRead

    try {
      /* create transaction & transaction details  */
      await mySQLWrapper.beginTransaction(conn)
      const transactionFields = {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        mobile_number: fields.merchant_mobile_number,
        aggregator_order_id: fields.aggregator_order_id,
        points_factor: fields.points_factor,
        transaction_id: fields.aggregator_order_id,
        amount: fields.amount,
        commission_amount: '',
        transaction_type: '86',
        transaction_status: 'S',
        remarks: fields.remarks || 'Subscription transaction',
        provider_name: 'Airpay',
        utility_name: 'SUBSCRIPTION - ' + fields.module_type.toUpperCase(),
        customer_name: fields.merchant_name,
        customer_mobile: fields.merchant_mobile_number,
        customer_email: '',
        form_data: {
          plan_duration: fields.plan_duration + ' months'
        }
      }
      const transactionResult = await TransactionController.initiateTransaction('_', transactionFields)
      log.logger({ pagename: path.basename(__filename), action: 'initiateTransaction', type: 'transactionResult', fields: transactionResult })
      if (transactionResult.status != 200) {
        await mySQLWrapper.rollback(conn)
        return transactionResult
      }

      await mySQLWrapper.commit(conn)
      return transactionResult
    } catch (err) {
      log.logger({ pagename: path.basename(__filename), action: 'initiateTransaction', type: 'err', fields: err })
      errorEmail.notifyCatchErrorEmail({
        function: 'initiateTransaction',
        data: { ...fields },
        error: err
      })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      if (isSet) conn.release()
      if (isSetRead) connRead.release()
    }
  }

  static async ledgerEntries (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'ledgerEntries', type: 'request', fields: fields })

    const ledgerEntriesCtrl = require('../ledgerEntries/ledgerEntriesController')

    const conn = await MySQLWrapper.getConnectionFromPool()

    // Expected fields
    // {ma_user_id,amount,userid,aggregator_order_id,transaction_charges}
    try {
      // Start
      await MySQLWrapper.beginTransaction(conn)
      const ledgerFields = {
        amount: fields.amount,
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        order_id: fields.aggregator_order_id,
        connection: conn,
        plan_duration: fields.plan_duration,
        module_type: fields.module_type

      }

      const ledger = await ledgerEntriesCtrl.subscriptionLedgerEntries(ledgerFields, conn)
      if (ledger.status === 400) {
        return ledgerFields
      }

      console.timeEnd('Timer_DebitTxnCharges')
      await MySQLWrapper.commit(conn)
      // End
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'ledgerEntries', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  static async subscriptionPlan (_, { ma_user_id, userid }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'Subscription', type: 'subscriptionSelection', fields: { ma_user_id, userid } })
    const connection = await mySQLWrapper.getConnectionFromReadReplica()
    try {
      const sql1 = `select subscription_status ,module_type from ma_merchant_subscription where subscription_status="A" and ma_user_id=${ma_user_id} and user_id=${userid} LIMIT 10`

      const sql2 = 'SELECT module_name,web_logo,app_logo,display_name  FROM ma_subscription_module where status="Y" LIMIT 10'
      log.logger({ pagename: require('path').basename(__filename), action: 'Subscription', type: 'subscriptionSelectionsqlquery', fields: { sql1, sql2 } })

      const subChannels = await this.rawQuery(sql2, connection)
      const subplansresponse = subChannels.map(({ module_name, web_logo, app_logo, display_name }) => ({
        name: module_name,
        web_logo,
        app_logo,
        plan_activation: false,
        display_name: display_name
      }))
      const subscription_status = await this.rawQuery(sql1, connection)
      if (subscription_status.length > 0) {
        const subPlans = subChannels.map(({ module_name, web_logo, app_logo, display_name }) => {
          const isActive = subscription_status.find(status =>
            status.subscription_status === 'A' && status.module_type === module_name
          ) !== undefined

          return {
            name: module_name,
            web_logo,
            app_logo,
            plan_activation: isActive,
            display_name: display_name
          }
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'Subscription', type: 'subChannels', fields: subChannels })
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: subPlans, action_code: 1000 }
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'Subscription', type: 'subscriptionSelection', fields: subplansresponse })
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: subplansresponse, action_code: 1000 }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'Subscription', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      // Releases the connection
      connection.release()
    }
  }

  static async subscriptionReceiptDetails (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'subscriptionReceiptDetails', type: 'request', fields: fields })
    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      let isRenewableSql = `
        SELECT 
          mtm.ma_user_id,
          mtm.userid,
          mtm.aggregator_txn_id,
          mms.module_type,
           mms.plan_start_date,
          mms.plan_end_date,
          mtm.amount,
          mms.subscription_status,
          mtm.transaction_status,
          mtm.mobile_number,
          mtm.transaction_reason,
          mtm.aggregator_order_id,
          mtm.ma_transaction_master_id,
          DATE_FORMAT(mtm.addedon,'%d-%m-%Y %h:%i:%s %p') AS addedon,
          mum.address,
          mum.firstname,
          mum.lastname,
          mum.email_id,
          msp.plan_duration
          FROM 
          ma_merchant_subscription mms
          LEFT JOIN 
          ma_transaction_master mtm ON mms.order_id = mtm.aggregator_order_id
          LEFT JOIN ma_user_master mum ON mms.ma_user_id = mum.profileid
          LEFT join ma_subscription_plans msp ON mms.ma_subscription_plan_id=msp.ma_subscription_plan_id
        WHERE 

          mms.ma_user_id = ${fields.ma_user_id} 
          AND mms.user_id = ${fields.userid}
          AND mtm.transaction_type = '${fields.transaction_type}'`

      if (fields.aggregator_order_id) {
        isRenewableSql += ` AND mtm.aggregator_order_id = '${fields.aggregator_order_id}'`
      }

      isRenewableSql += ' ORDER BY mtm.ma_transaction_master_id DESC  Limit 1'
      log.logger({ pagename: require('path').basename(__filename), action: 'subscriptionReceiptDetails', type: 'request', isRenewableSql })
      const updateData = await this.rawQuery(isRenewableSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'subscriptionReceiptDetails', type: 'request', updateData })
      const transaction_receipt_data = updateData.map(item => ({
        order_id: item.aggregator_order_id,
        amount: item.amount,
        customer_name: item.firstname + ' ' + item.lastname,
        module_type: item.module_type.toUpperCase(),
        plan_duration: item.plan_duration,
        transaction_date_time: item.addedon,
        transaction_status: item.transaction_status,
        aggregator_txn_id: item.aggregator_order_id,
        aggregator_order_id: item.aggregator_order_id,
        mobile_number: item.mobile_number,
        transaction_reason: item.transaction_reason,
        userid: item.userid,
        address: item.address,
        customer_emailId: item.email_id

      }))
      log.logger({ pagename: require('path').basename(__filename), action: 'subscriptionReceiptDetails', type: 'request', transaction_receipt_data })
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, action_code: 1000, receipt_bill_response: transaction_receipt_data }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'subscriptionReceiptDetails', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release Connection
      if (connection) {
        connection.release()
      }
    }
  }
}

module.exports = SubscriptionController
