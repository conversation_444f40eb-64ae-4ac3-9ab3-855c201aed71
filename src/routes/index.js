const middleware = require('../util/middleware')
const login = require('./login')
const util = require('../util/util')

const routeEndpoints = ['login', 'profile', 'cashbalance', 'pointsbalance', 'pointsLedger', 'cashLedger', 'user', 'sendMoney', 'useractionlog', 'pointsToCash', 'topup', 'cashWithdrawal', 'globalPointsRate', 'refund', 'incentive', 'recon', 'cashWithdrawalNeft', 'aeps', 'cashLedgerHistory', 'pointsLedgerHistory', 'authentication', 'useractionlog', 'transaction', 'accountdetails', 'custlogin', 'beneficiary', 'acceptkyc', 'transfers', 'currentkycstatus', 'aml', 'bankdetails', 'refundtransfer', 'tickets', 'supportTicket', 'securepin', 'leads', 'billpay', 'insurance', 'evalue', 'banklist', 'notice', 'migration', 'cms', 'transactionstatus', 'changepassword', 'khatabook', 'gold', 'goldagentbranch', 'remitterlogin', 'beneverify', 'posactivation', 'adhoc', 'ekyc', 'recharge', 'shopping', 'requestcredit', 'cashbackpromotion', 'khatabookCustomerV2', 'khatabookAccountV2', 'khatabookTransactionV2', 'recharge', 'bbpsinsurance', 'electricity', 'qractivation', 'withdrawalhistory', 'merchantdashboard', 'intratransfers', 'bbps', 'apirouter', 'aepstransaction', 'salepromotions', 'bbpsnewbill', 'logout', 'bbpsproviderspecific', 'ondc', 'umang', 'ondcv2', 'loanlead', 'nearbymerchanttoken', 'nearbymerchantlist', 'webformtoken', 'aepsmerchantauth', 'rekyc', 'braingymjr', 'promotions', 'fmt', 'prepaidcard', 'ondcseller', 'soundbox', 'ppiwallet', 'advancecredit', 'subscription', 'finosuvidha', 'commissionreport']

module.exports = class Routes {
  /**
     * Applies the routes to specific paths
     * @param {*} app - The instance of express which will be serving requests.
     */
  constructor (app) {
    // Throws if no instance of express was passed
    if (app == null) throw new Error('You must provide an instance of express')

    app.use('/login', login)

    if (!util.debug) app.use(middleware.JwtMiddleware)
    app.use(middleware.sizeLimiting)
    // Registers the base GraphQLi base endpoint

    app.use((req, res, next) => {
      req.routeEndpoints = routeEndpoints
      // middleware.logRequest(req, res, next)
      middleware.logs(req, res, next)
    })
    // app.use(middleware.logRequest)

    routeEndpoints.forEach(function (name) {
      const nameRoute = require('./' + name)
      app.use('/' + name, nameRoute)
    })
  }
}
