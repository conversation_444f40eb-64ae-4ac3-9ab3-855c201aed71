const {
  GraphQLNonNull,
  GraphQLString,
  GraphQLInt,
  GraphQLInputObjectType,
  GraphQLList,
  GraphQLBoolean,
  GraphQLEnumType
} = require('graphql')
const type = require('./type')
const commonEnum = require('../commonEnum')
const commonScalar = require('../commonScalar')
const transfer = require('../../controller/transfers/transfersController')
const refund = require('../../controller/refund/refundtransferController')
const retry = require('../../controller/transfers/retryController')
const transfersReQuery = require('../../controller/transfers/transfersReQuery')

module.exports = {
  acceptAmount: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      amount: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      },
      uic: {
        type: new GraphQLNonNull(GraphQLString)
      },
      mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_beneficiaries_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      transfer_mode: {
        type: commonEnum.transferMode
      },
      sessionRQ: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_bank_type_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      beneficiary_name: {
        type: GraphQLString
      }
    },
    resolve: transfer.acceptAmountV2.bind(transfer)
  },
  confirmAmount: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      amount: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      },
      transaction_charges: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      },
      uic: {
        type: new GraphQLNonNull(GraphQLString)
      },
      mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_beneficiaries_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      pan: {
        type: GraphQLString
      },
      transfer_list: {
        type: new GraphQLNonNull(commonEnum.reconStatus)
      },
      transfer_mode: {
        type: commonEnum.transferMode
      },
      security_pin: {
        type: new GraphQLNonNull(GraphQLString)
      },
      sessionRQ: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_bank_type_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      bene_mobile_number: {
        type: GraphQLString
      },
      denomination_form_data: {
        type: GraphQLString
      },
      remarks: {
        type: GraphQLString
      },
      PID: {
        type: GraphQLString
      },
      otp_ref_id: {
        type: GraphQLString
      },
      otp: {
        type: GraphQLString
      },
      latitude: {
        type: GraphQLString
      },
      longitude: {
        type: GraphQLString
      },
      public_ip: {
        type: GraphQLString
      },
      token: {
        type: GraphQLString
      }
    },
    resolve: transfer.confirmAmountV2.bind(transfer)
  },
  refund: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_transfers_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uic: {
        type: new GraphQLNonNull(GraphQLString)
      },
      mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      orderid: {
        type: GraphQLString
      },
      otp: {
        type: GraphQLString
      },
      action: {
        type: new GraphQLNonNull(commonEnum.kycOtpEnum)
      },
      airpayotp: {
        type: GraphQLBoolean
      },
      sessionRQ: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: refund.refundTransfer.bind(refund)
  },
  retry: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_transaction_master_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      uic: {
        type: new GraphQLNonNull(GraphQLString)
      },
      mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: retry.transfers.bind(retry)
  },
  reCheckTransferStatus: {
    type,
    args: {
      limit: {
        type: GraphQLInt
      },
      interval: {
        type: GraphQLInt
      }
    },
    resolve: transfersReQuery.reCheckTransferStatus.bind(transfersReQuery)
  },
  refundTransferForCron: {
    type,
    args: {
      ma_transfers_id: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: refund.refundTransferForCron.bind(refund)
  },
  reCheckPendingIncentives: {
    type,
    args: {
      limit: {
        type: GraphQLString
      },
      interval: {
        type: GraphQLString
      },
      all: {
        type: GraphQLString
      },
      ma_transaction_master_id: {
        type: GraphQLString
      }
    },
    resolve: transfer.reCheckPendingIncentives.bind(transfer)
  },
  trywithAcceptAmount: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ref_aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      sessionRQ: {
        type: GraphQLString
      },
      ma_bank_type_id: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: transfer.trywithAcceptAmount.bind(transfer)
  },
  trywithConfirmAmount: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      amount: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      },
      transaction_charges: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      },
      uic: {
        type: new GraphQLNonNull(GraphQLString)
      },
      mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_beneficiaries_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      pan: {
        type: GraphQLString
      },
      transfer_list: {
        type: new GraphQLNonNull(commonEnum.reconStatus)
      },
      transfer_mode: {
        type: commonEnum.transferMode
      },
      security_pin: {
        type: new GraphQLNonNull(GraphQLString)
      },
      sessionRQ: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_bank_type_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ref_aggregator_order_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      bene_mobile_number: {
        type: GraphQLString
      }
    },
    resolve: transfer.trywithConfirmAmount.bind(transfer)
  },
  bulkConfirmAmount: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      amount: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      },
      transaction_charges: {
        type: new GraphQLNonNull(commonScalar.amountScalar)
      },
      uic: {
        type: new GraphQLNonNull(GraphQLString)
      },
      mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_beneficiaries_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      pan: {
        type: GraphQLString
      },
      transfer_list: {
        type: new GraphQLNonNull(commonEnum.reconStatus)
      },
      transfer_mode: {
        type: commonEnum.transferMode
      },
      security_pin: {
        type: new GraphQLNonNull(GraphQLString)
      },
      sessionRQ: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_bank_type_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      bene_mobile_number: {
        type: GraphQLString
      },
      aggregator_order_id: {
        type: GraphQLString
      },
      denomination_form_data: {
        type: GraphQLString
      },
      remarks: {
        type: GraphQLString
      },
      PID: {
        type: GraphQLString
      },
      otp_ref_id: {
        type: GraphQLString
      },
      otp: {
        type: GraphQLString
      },
      latitude: {
        type: GraphQLString
      },
      longitude: {
        type: GraphQLString
      },
      public_ip: {
        type: GraphQLString
      },
      token: {
        type: GraphQLString
      }
    },
    resolve: transfer.bulkConfirmAmount.bind(transfer)
  },
  generateCustomerOTP: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_bank_on_boarding_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      otp_type: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      latitude: {
        type: GraphQLString
      },
      longitude: {
        type: GraphQLString
      },
      sessionRQ: {
        type: new GraphQLNonNull(GraphQLString)
      },
      uic: {
        type: new GraphQLNonNull(GraphQLString)
      },
      amount: {
        type: GraphQLString
      },
      ma_beneficiaries_id: {
        type: GraphQLInt
      },
      request_source: {
        type: GraphQLString
      },
      acToken: {
        type: GraphQLString
      },
      ifsc_code: {
        type: GraphQLString
      },
      account_number: {
        type: GraphQLString
      },
      ma_transfers_id: {
        type: GraphQLInt
      },
      request_number: {
        type: GraphQLString
      },
      aggregator_order_id: {
        type: GraphQLString
      },
      flag: {
        type: GraphQLInt
      },
      bank_charges: {
        type: commonScalar.amountScalar
      }
    },
    resolve: transfer.generateCustomerOTP.bind(transfer)
  },
  submitCustomerEKYC: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      bank_on_boarding_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      remitter_mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      remitter_aadhaar_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      pid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      kyc_type_flag: {
        type: GraphQLInt
      },
      latitude: {
        type: GraphQLString
      },
      longitude: {
        type: GraphQLString
      },
      sessionRQ: {
        type: new GraphQLNonNull(GraphQLString)
      },
      uic: {
        type: new GraphQLNonNull(GraphQLString)
      },
      request_source: {
        type: GraphQLString
      }
    },
    resolve: transfer.submitCustomerEKYC.bind(transfer)
  },
  initiateCustomerOnboardingDMT: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      bank_on_boarding_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      mobile_no: {
        type: GraphQLString
      },
      latitude: {
        type: GraphQLString
      },
      longitude: {
        type: GraphQLString
      },
      sessionRQ: {
        type: GraphQLString
      },
      uic: {
        type: GraphQLString
      },
      request_source: {
        type: GraphQLString
      }
    },
    resolve: transfer.initiateCustomerOnboardingDMT.bind(transfer)
  },
  processCustomerRegistration: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      customer_mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      otp_request_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      otp_pin: {
        type: new GraphQLNonNull(GraphQLString)
      },
      kyc_request_id: {
        type: GraphQLString
      },
      sessionRQ: {
        type: new GraphQLNonNull(GraphQLString)
      },
      uic: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: transfer.processCustomerRegistration.bind(transfer)
  },
  submitCustomerAirtelEKYC: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      bank_on_boarding_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      remitter_mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      pid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      kyc_type_flag: {
        type: GraphQLInt
      },
      sessionRQ: {
        type: new GraphQLNonNull(GraphQLString)
      },
      uic: {
        type: new GraphQLNonNull(GraphQLString)
      },
      request_source: {
        type: GraphQLString
      },
      otpToken: {
        type: GraphQLString
      }
    },
    resolve: transfer.submitCustomerAirtelEKYC.bind(transfer)
  },
  verifyAadhaarDetails: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      bank_on_boarding_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      remitter_mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      remitter_aadhaar_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      sessionRQ: {
        type: new GraphQLNonNull(GraphQLString)
      },
      uic: {
        type: new GraphQLNonNull(GraphQLString)
      },
      request_source: {
        type: GraphQLString
      },
      acToken: {
        type: GraphQLString
      }
    },
    resolve: transfer.verifyAadhaarDetails.bind(transfer)
  },
  verifyBiometricDetails: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      bank_on_boarding_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      remitter_mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      remitter_aadhaar_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      pid: {
        type: new GraphQLNonNull(GraphQLString)
      },
      sessionRQ: {
        type: new GraphQLNonNull(GraphQLString)
      },
      uic: {
        type: new GraphQLNonNull(GraphQLString)
      },
      request_source: {
        type: GraphQLString
      },
      otpToken: {
        type: GraphQLString
      }
    },
    resolve: transfer.verifyBiometricDetails.bind(transfer)
  },
  verifyCustomerOTP: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_bank_on_boarding_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      otp_type: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      latitude: {
        type: GraphQLString
      },
      longitude: {
        type: GraphQLString
      },
      sessionRQ: {
        type: new GraphQLNonNull(GraphQLString)
      },
      uic: {
        type: new GraphQLNonNull(GraphQLString)
      },
      amount: {
        type: GraphQLString
      },
      otp_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      otp_pin: {
        type: new GraphQLNonNull(GraphQLString)
      },
      request_source: {
        type: GraphQLString
      },
      acToken: {
        type: GraphQLString
      }
    },
    resolve: transfer.verifyCustomerOTP.bind(transfer)
  },
  validateRemitterAadhaar: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      aadhaar_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      sessionToken: {
        type: new GraphQLNonNull(GraphQLString)
      },
      sessionRQ: {
        type: new GraphQLNonNull(GraphQLString)
      },
      uic: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: transfer.validateRemitterAadhaar.bind(transfer)
  },
  verifyTransfer: {
    type: type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      sessionRQ: {
        type: new GraphQLNonNull(GraphQLString)
      },
      uic: {
        type: new GraphQLNonNull(GraphQLString)
      },
      mobile_number: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_transfers_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      otp_id: {
        type: new GraphQLNonNull(GraphQLString)
      },
      otp_pin: {
        type: new GraphQLNonNull(GraphQLString)
      },
      ma_beneficiaries_id: {
        type: GraphQLInt
      },
      amount: {
        type: commonScalar.amountScalar
      },
      request_number: {
        type: GraphQLString
      },
      bank_charges: {
        type: commonScalar.amountScalar
      },
      aggregator_order_id: {
        type: GraphQLString
      },
      transfer_mode: {
        type: commonEnum.transferMode
      },
      account_number: {
        type: GraphQLString
      },
      ifsc_code: {
        type: GraphQLString
      },
      remitter_id: {
        type: GraphQLString
      },
      receiver_id: {
        type: GraphQLString
      },
      PID: {
        type: GraphQLString
      },
      action: {
        type: GraphQLString
      }
    },
    resolve: transfer.verifyTransfer.bind(transfer)
  }
}
