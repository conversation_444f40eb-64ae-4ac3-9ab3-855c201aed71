const { GraphQLObjectType } = require('graphql')
const advanceCreditMutation = require('../../model/ma_advance_credit/mutation')

module.exports = new GraphQLObjectType({
  name: 'RootMutationsType',
  fields: {
    getAdvanceCreditForm: advanceCreditMutation.getAdvanceCreditForm,
    requestGeneration: advanceCreditMutation.requestGeneration,
    submitMerchantDetails: advanceCreditMutation.submitMerchantDetails,
    resendOtp: advanceCreditMutation.resendOtp,
    verifyOtp: advanceCreditMutation.verifyOtp,
    submitQuoteAmt: advanceCreditMutation.submitQuoteAmt,
    generateEsignRequest: advanceCreditMutation.generateEsignRequest,
    generateEsignResponse: advanceCreditMutation.generateEsignResponse,
    submitSignedAgreement: advanceCreditMutation.submitSignedAgreement
  }
})
